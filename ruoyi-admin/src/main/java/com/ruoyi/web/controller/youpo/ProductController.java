package com.ruoyi.web.controller.youpo;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestParam;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Product;
import com.ruoyi.system.domain.Sku;
import com.ruoyi.system.service.IProductService;
import com.ruoyi.system.service.ISkuService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商品信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Controller
@RequestMapping("/youpo/product")
public class ProductController extends BaseController
{
    private String prefix = "youpo/product";

    @Autowired
    private IProductService productService;

    @Autowired
    private ISkuService skuService;

    //@RequiresPermissions("system:product:view")
    @GetMapping()
    public String product()
    {
        return prefix + "/product";
    }

    /**
     * 查询商品信息列表
     */
    //@RequiresPermissions("system:product:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Product product)
    {
        startPage();
        List<Product> list = productService.selectProductList(product);
        return getDataTable(list);
    }

    /**
     * 导出商品信息列表
     */
    //@RequiresPermissions("system:product:export")
    @Log(title = "商品信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Product product)
    {
        List<Product> list = productService.selectProductList(product);
        ExcelUtil<Product> util = new ExcelUtil<Product>(Product.class);
        return util.exportExcel(list, "商品信息数据");
    }

    /**
     * 新增商品信息
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存商品信息
     */
    //@RequiresPermissions("system:product:add")
    @Log(title = "商品信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Product product)
    {
        return toAjax(productService.insertProduct(product));
    }

    /**
     * 修改商品信息
     */
    @GetMapping("/edit/{productId}")
    public String edit(@PathVariable("productId") Long productId, ModelMap mmap)
    {
        Product product = productService.selectProductByProductId(productId);
        mmap.put("product", product);
        return prefix + "/edit";
    }

    /**
     * 修改保存商品信息
     */
    //@RequiresPermissions("system:product:edit")
    @Log(title = "商品信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Product product)
    {
        return toAjax(productService.updateProduct(product));
    }

    /**
     * 复用商品信息
     */
    //@RequiresPermissions("system:product:add")
    @GetMapping("/copy/{productId}")
    public String copy(@PathVariable("productId") Long productId, ModelMap mmap)
    {
        Product product = productService.selectProductByProductId(productId);
        // 清空ID，不复制这个内容
        product.setProductId(null);

        mmap.put("product", product);

        // 查询该商品的所有SKU
        Sku skuQuery = new Sku();
        skuQuery.setProductId(productId);
        List<Sku> skuList = skuService.selectSkuList(skuQuery);
        mmap.put("skuList", skuList);

        return prefix + "/copy";
    }

    /**
     * 保存复用的商品和SKU
     */
    //@RequiresPermissions("system:product:add")
    @Log(title = "商品信息", businessType = BusinessType.INSERT)
    @PostMapping("/copyWithSkus")
    @ResponseBody
    public AjaxResult copyWithSkus(
            Product product,
            @RequestParam(required = false) String skuData)
    {
        try {
            // 调用Service层处理复制商品及SKU的业务逻辑
            int result = productService.copyWithSkus(product, skuData);
            if (result > 0) {
                return AjaxResult.success("复制商品成功");
            } else {
                return AjaxResult.error("复制商品失败");
            }
        } catch (Exception e) {
            logger.error("复制商品及SKU失败", e);
            return AjaxResult.error("复制商品及SKU失败: " + e.getMessage());
        }
    }

    /**
     * 获取商品信息（AJAX接口）
     */
    @GetMapping("/getInfo/{productId}")
    @ResponseBody
    public AjaxResult getInfo(@PathVariable("productId") Long productId) {
        Product product = productService.selectProductByProductId(productId);
        return AjaxResult.success(product);
    }

    /**
     * 删除商品信息
     */
    //@RequiresPermissions("system:product:remove")
    @Log(title = "商品信息", businessType = BusinessType.DELETE)
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(productService.deleteProductByProductIds(ids));
    }

    /**
     * 校验商品编码是否唯一
     */
    @PostMapping("/checkProductCodeUnique")
    @ResponseBody
    public String checkProductCodeUnique(Product product)
    {
        return productService.checkProductCodeUnique(product);
    }
}