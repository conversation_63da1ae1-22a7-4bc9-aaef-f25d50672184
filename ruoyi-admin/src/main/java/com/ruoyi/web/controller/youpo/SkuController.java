package com.ruoyi.web.controller.youpo;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.system.domain.Product;
import com.ruoyi.system.service.IProductService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.Sku;
import com.ruoyi.system.service.ISkuService;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * SKU信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Controller
@RequestMapping("/youpo/sku")
public class SkuController extends BaseController
{
    private String prefix = "youpo/sku";

    @Autowired
    private ISkuService skuService;
    @Autowired
    private IProductService productService;

    //@RequiresPermissions("system:sku:view")
    @GetMapping()
    public String sku(Long productId, String productName, String productCode, ModelMap mmap)
    {
        if (productId != null) {
            mmap.put("productId", productId);
            // 直接使用传入的商品信息，无需再查询数据库
            if (productName != null) {
                mmap.put("productName", productName);
            }
            if (productCode != null) {
                mmap.put("productCode", productCode);
            }

            // 获取商品的默认价格，用于批量修改
            Product product = productService.selectProductByProductId(productId);
            if (product != null) {
                mmap.put("defaultCostPrice", product.getDefaultCostPrice());
                mmap.put("defaultSalePrice", product.getDefaultSalePrice());
            }
        }
        return prefix + "/sku";
    }

    /**
     * 查询SKU信息列表
     */
    //@RequiresPermissions("system:sku:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(Sku sku)
    {
        startPage();
        List<Sku> list = skuService.selectSkuList(sku);
        return getDataTable(list);
    }

    /**
     * 导出SKU信息列表
     */
    //@RequiresPermissions("system:sku:export")
    @Log(title = "SKU信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(Sku sku)
    {
        List<Sku> list = skuService.selectSkuList(sku);
        ExcelUtil<Sku> util = new ExcelUtil<Sku>(Sku.class);
        return util.exportExcel(list, "SKU信息数据");
    }

    /**
     * 新增SKU信息
     */
    @GetMapping("/add")
    public String add(Long productId, ModelMap mmap)
    {
        if (productId != null) {
            mmap.put("productId", productId);

            // 获取商品信息，用于预填充默认参数
            Product product = productService.selectProductByProductId(productId);
            if (product != null) {
                mmap.put("product", product);
            }
        }
        return prefix + "/add";
    }

    /**
     * 新增保存SKU信息
     */
    //@RequiresPermissions("system:sku:add")
    @Log(title = "SKU信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(Sku sku)
    {
        return toAjax(skuService.insertSku(sku));
    }

    /**
     * 修改SKU信息
     */
    @GetMapping("/edit/{skuId}")
    public String edit(@PathVariable("skuId") Long skuId, ModelMap mmap)
    {
        Sku sku = skuService.selectSkuBySkuId(skuId);
        mmap.put("sku", sku);
        return prefix + "/edit";
    }

    /**
     * 修改保存SKU信息
     */
    //@RequiresPermissions("system:sku:edit")
    @Log(title = "SKU信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(Sku sku)
    {
        return toAjax(skuService.updateSku(sku));
    }


    /**
     * 复用SKU信息
     */
    //@RequiresPermissions("system:sku:add")
    @GetMapping("/copy/{skuId}")
    public String copy(@PathVariable("skuId") Long skuId, ModelMap mmap)
    {
        Sku sku = skuService.selectSkuBySkuId(skuId);
        // 清空ID和库存相关信息，不复制这些内容
        sku.setSkuId(null);
        sku.setSkuCode(null); // 要求用户输入新的SKU编码

        // 初始化库存为0，不复制原有库存
        sku.setCurrentStock(0L);
        sku.setAvailableStock(0L);
        sku.setLockedStock(0L);
        sku.setTransitStock(0L);

        mmap.put("sku", sku);
        return prefix + "/copy";
    }

    /**
     * 获取SKU信息（AJAX接口）
     */
    @GetMapping("/getInfo/{skuId}")
    @ResponseBody
    public AjaxResult getInfo(@PathVariable("skuId") Long skuId) {
        Sku sku = skuService.selectSkuBySkuId(skuId);
        return AjaxResult.success(sku);
    }

    /**
     * 删除SKU信息
     */
    //@RequiresPermissions("system:sku:remove")
    @Log(title = "SKU信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(skuService.deleteSkuBySkuIds(ids));
    }

    /**
     * 校验SKU编码是否唯一
     */
    @PostMapping("/checkSkuCodeUnique")
    @ResponseBody
    public String checkSkuCodeUnique(Sku sku)
    {
        return skuService.checkSkuCodeUnique(sku);
    }

    /**
     * 批量修改SKU价格
     */
    //@RequiresPermissions("system:sku:edit")
    @Log(title = "批量修改SKU价格", businessType = BusinessType.UPDATE)
    @PostMapping("/batchModifyPrice")
    @ResponseBody
    public AjaxResult batchModifyPrice(String skuIds, BigDecimal costPrice, BigDecimal salePrice)
    {
        return toAjax(skuService.batchUpdatePrice(skuIds, costPrice, salePrice));
    }

    /**
     * 批量修改SKU信息
     */
    //@RequiresPermissions("system:sku:edit")
    @Log(title = "批量修改SKU信息", businessType = BusinessType.UPDATE)
    @PostMapping("/batchModify")
    @ResponseBody
    public AjaxResult batchModify(
            String skuIds,
            BigDecimal costPrice,
            BigDecimal salePrice,
            String color,
            String size,
            Long currentStock,
            Long stockWarning,
            BigDecimal weight,
            BigDecimal volume,
            Integer status)
    {
        return toAjax(skuService.batchUpdateSku(skuIds, costPrice, salePrice,
                color, size, currentStock, stockWarning, weight, volume, status));
    }
}