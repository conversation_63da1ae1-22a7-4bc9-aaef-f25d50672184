package com.ruoyi.web.controller.youpo;

import java.util.*;
import java.math.BigDecimal;

import com.ruoyi.system.domain.*;
import com.ruoyi.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.DateUtils;

/**
 * 销售订单Controller
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Controller
@RequestMapping("/youpo/salesOrder")
public class SalesOrderController extends BaseController
{
    private String prefix = "youpo/salesOrder";

    @Autowired
    private ISalesOrderService salesOrderService;

    @Autowired
    private ISalesOrderDetailService salesOrderDetailService;

    @Autowired
    private ISkuService skuService;

    @Autowired
    private IProductService productService;
    @Autowired
    private ICustomerService customerService;

    @Autowired
    private IProductComboService productComboService;

    @Autowired
    private IProductComboItemService productComboItemService;

    @GetMapping()
    public String salesOrder()
    {
        return prefix + "/salesOrder";
    }

    /**
     * 查询销售订单列表
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(SalesOrder salesOrder)
    {
        startPage();
        List<SalesOrder> list = salesOrderService.selectSalesOrderList(salesOrder);
        return getDataTable(list);
    }

    /**
     * 导出销售订单列表
     */
    @Log(title = "销售订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(SalesOrder salesOrder)
    {
        List<SalesOrder> list = salesOrderService.selectSalesOrderList(salesOrder);
        ExcelUtil<SalesOrder> util = new ExcelUtil<SalesOrder>(SalesOrder.class);
        return util.exportExcel(list, "销售订单数据");
    }

    /**
     * 新增销售订单
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }



    /**
     * 修改销售订单
     */
    @GetMapping("/edit/{orderId}")
    public String edit(@PathVariable("orderId") Long orderId, ModelMap mmap)
    {
        SalesOrder salesOrder = salesOrderService.selectSalesOrderByOrderId(orderId);
        mmap.put("salesOrder", salesOrder);

        // 查询订单明细
        SalesOrderDetail detail = new SalesOrderDetail();
        detail.setOrderId(orderId);
        List<SalesOrderDetail> details = salesOrderDetailService.selectSalesOrderDetailList(detail);

        // 为每个明细添加productId信息
        for (SalesOrderDetail orderDetail : details) {
            if (orderDetail.getSkuId() != null) {
                Sku sku = skuService.selectSkuBySkuId(orderDetail.getSkuId());
                if (sku != null) {
                    // 将productId存储在一个临时字段中，我们需要在SalesOrderDetail中添加这个字段
                    orderDetail.setRemark(sku.getProductId().toString()); // 临时使用remark字段存储productId
                }
            }
        }

        mmap.put("details", details);

        return prefix + "/edit";
    }

    /**
     * 新增保存销售订单
     */
    @Log(title = "销售订单", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(SalesOrder salesOrder,
                              @RequestParam(value = "skuIds[]", required = false) Long[] skuIds,
                              @RequestParam(value = "quantities[]", required = false) Long[] quantities,
                              @RequestParam(value = "unitPrices[]", required = false) BigDecimal[] unitPrices,
                              @RequestParam(value = "discounts[]", required = false) BigDecimal[] discounts) {
        return toAjax(salesOrderService.createSalesOrder(salesOrder, skuIds, quantities,
                unitPrices, discounts));
    }

    /**
     * 修改保存销售订单
     */
    @Log(title = "销售订单", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(SalesOrder salesOrder,
                               @RequestParam(value = "deletedDetailIds[]", required = false) Long[] deletedDetailIds,
                               @RequestParam(required = false) Map<String, String> params) {

        // 处理数据，从params中提取existingQuantities、existingDiscounts和existingSkuIds
        Map<Long, Long> existingQuantities = new HashMap<>();
        Map<Long, BigDecimal> existingDiscounts = new HashMap<>();
        Map<Long, Long> existingSkuIds = new HashMap<>();

        // 处理新增的SKU
        List<Long> newSkuIdsList = new ArrayList<>();
        List<Long> newQuantitiesList = new ArrayList<>();
        List<BigDecimal> newUnitPricesList = new ArrayList<>();
        List<BigDecimal> newDiscountsList = new ArrayList<>();

        // 从请求参数中提取数据
        for (Map.Entry<String, String> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            // 处理已有明细的数量
            if (key.startsWith("existingQuantities[") && key.endsWith("]")) {
                String detailIdStr = key.substring("existingQuantities[".length(), key.length() - 1);
                try {
                    Long detailId = Long.parseLong(detailIdStr);
                    Long quantity = Long.parseLong(value);
                    existingQuantities.put(detailId, quantity);
                } catch (NumberFormatException e) {
                    // 忽略无效数据
                }
            }

            // 处理已有明细的优惠金额
            else if (key.startsWith("existingDiscounts[") && key.endsWith("]")) {
                String detailIdStr = key.substring("existingDiscounts[".length(), key.length() - 1);
                try {
                    Long detailId = Long.parseLong(detailIdStr);
                    BigDecimal discount = new BigDecimal(value);
                    existingDiscounts.put(detailId, discount);
                } catch (NumberFormatException e) {
                    // 忽略无效数据
                }
            }

            // 处理已有明细的SKU ID
            else if (key.startsWith("existingSkuIds[") && key.endsWith("]")) {
                String detailIdStr = key.substring("existingSkuIds[".length(), key.length() - 1);
                try {
                    Long detailId = Long.parseLong(detailIdStr);
                    Long skuId = Long.parseLong(value);
                    existingSkuIds.put(detailId, skuId);
                } catch (NumberFormatException e) {
                    // 忽略无效数据
                }
            }

            // 处理新增SKU
            else if (key.equals("newSkuIds[]")) {
                String[] values = params.get(key).split(",");
                for (String v : values) {
                    if (!v.trim().isEmpty()) {
                        newSkuIdsList.add(Long.parseLong(v.trim()));
                    }
                }
            }
            else if (key.equals("newQuantities[]")) {
                String[] values = params.get(key).split(",");
                for (String v : values) {
                    if (!v.trim().isEmpty()) {
                        newQuantitiesList.add(Long.parseLong(v.trim()));
                    }
                }
            }
            else if (key.equals("newUnitPrices[]")) {
                String[] values = params.get(key).split(",");
                for (String v : values) {
                    if (!v.trim().isEmpty()) {
                        newUnitPricesList.add(new BigDecimal(v.trim()));
                    }
                }
            }
            else if (key.equals("newDiscounts[]")) {
                String[] values = params.get(key).split(",");
                for (String v : values) {
                    if (!v.trim().isEmpty()) {
                        newDiscountsList.add(new BigDecimal(v.trim()));
                    }
                }
            }
        }

        // 转换为数组
        Long[] newSkuIds = newSkuIdsList.isEmpty() ? null : newSkuIdsList.toArray(new Long[0]);
        Long[] newQuantities = newQuantitiesList.isEmpty() ? null : newQuantitiesList.toArray(new Long[0]);
        BigDecimal[] newUnitPrices = newUnitPricesList.isEmpty() ? null : newUnitPricesList.toArray(new BigDecimal[0]);
        BigDecimal[] newDiscounts = newDiscountsList.isEmpty() ? null : newDiscountsList.toArray(new BigDecimal[0]);

        // 调用服务方法
        return toAjax(salesOrderService.updateSalesOrderWithDiscounts(salesOrder, deletedDetailIds,
                existingQuantities, existingDiscounts, existingSkuIds, newSkuIds,
                newQuantities, newUnitPrices, newDiscounts));
    }

    /**
     * 删除销售订单
     */
    @Log(title = "销售订单", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(salesOrderService.deleteSalesOrderByOrderIds(ids));
    }

    /**
     * 查询商品信息
     */
    @PostMapping("/searchProducts")
    @ResponseBody
    public AjaxResult searchProducts(String keyword)
    {
        Product product = new Product();
        product.setProductName(keyword);
        List<Product> products = productService.selectProductList(product);
        return AjaxResult.success(products);
    }

    /**
     * 查询SKU信息
     */
    @PostMapping("/searchSkus")
    @ResponseBody
    public AjaxResult searchSkus(Long productId, String keyword)
    {

        List<Sku> skus = skuService.selectSkuListWithKeyword(productId,keyword);
        return AjaxResult.success(skus);
    }

    /**
     * 查询客户信息
     */
    @PostMapping("/searchCustomers")
    @ResponseBody
    public AjaxResult searchCustomers(String keyword)
    {
        Customer customer = new Customer();
        customer.setCustomerName(keyword);
        List<Customer> customers = customerService.selectCustomerList(customer);
        return AjaxResult.success(customers);
    }

    /**
     * 查询商品组合
     */
    @PostMapping("/searchProductCombos")
    @ResponseBody
    public AjaxResult searchProductCombos(String keyword) {
        ProductCombo query = new ProductCombo();
        if (keyword != null && !keyword.isEmpty()) {
            query.setComboName(keyword);
        }
        query.setStatus("1"); // 只查询启用状态的组合
        List<ProductCombo> combos = productComboService.selectProductComboList(query);
        return AjaxResult.success(combos);
    }

    /**
     * 获取商品组合项目详情
     */
    @GetMapping("/getProductComboItems/{comboId}")
    @ResponseBody
    public AjaxResult getProductComboItems(@PathVariable("comboId") Long comboId) {
        List<Map<String, Object>> items = productComboItemService.selectProductComboItemsWithInfo(comboId);
        return AjaxResult.success(items);
    }

    /**
     * 打印销售订单（含价格）
     */
    @GetMapping("/orderPrint/{orderId}")
    public String orderPrint(@PathVariable("orderId") Long orderId, ModelMap mmap) {
        SalesOrder salesOrder = salesOrderService.selectSalesOrderByOrderId(orderId);
        mmap.put("salesOrder", salesOrder);

        // 查询订单明细
        SalesOrderDetail detail = new SalesOrderDetail();
        detail.setOrderId(orderId);
        List<SalesOrderDetail> details = salesOrderDetailService.selectSalesOrderDetailList(detail);
        mmap.put("details", details);

        return prefix + "/orderPrint";
    }

    /**
     * 打印出库单（不含价格）
     */
    @GetMapping("/outboundPrint/{orderId}")
    public String outboundPrint(@PathVariable("orderId") Long orderId, ModelMap mmap) {
        SalesOrder salesOrder = salesOrderService.selectSalesOrderByOrderId(orderId);
        mmap.put("salesOrder", salesOrder);

        // 查询订单明细
        SalesOrderDetail detail = new SalesOrderDetail();
        detail.setOrderId(orderId);
        List<SalesOrderDetail> details = salesOrderDetailService.selectSalesOrderDetailList(detail);
        mmap.put("details", details);

        return prefix + "/outboundPrint";
    }

    /**
     * 获取订单信息（AJAX接口）
     */
    @GetMapping("/getOrderInfo/{orderId}")
    @ResponseBody
    public AjaxResult getOrderInfo(@PathVariable("orderId") Long orderId) {
        SalesOrder salesOrder = salesOrderService.selectSalesOrderByOrderId(orderId);
        return AjaxResult.success(salesOrder);
    }

    /**
     * 获取SKU信息（AJAX接口）
     */
    @GetMapping("/getSkuInfo/{skuId}")
    @ResponseBody
    public AjaxResult getSkuInfo(@PathVariable("skuId") Long skuId) {
        Sku sku = skuService.selectSkuBySkuId(skuId);
        return AjaxResult.success(sku);
    }

    /**
     * 获取商品信息（AJAX接口）
     */
    @GetMapping("/getProductInfo/{productId}")
    @ResponseBody
    public AjaxResult getProductInfo(@PathVariable("productId") Long productId) {
        Product product = productService.selectProductByProductId(productId);
        return AjaxResult.success(product);
    }

    /**
     * 查看销售订单详情
     */
    @GetMapping("/detail/{orderId}")
    public String detail(@PathVariable("orderId") Long orderId, ModelMap mmap) {
        SalesOrder salesOrder = salesOrderService.selectSalesOrderByOrderId(orderId);
        mmap.put("salesOrder", salesOrder);

        // 查询订单明细
        SalesOrderDetail detail = new SalesOrderDetail();
        detail.setOrderId(orderId);
        List<SalesOrderDetail> details = salesOrderDetailService.selectSalesOrderDetailList(detail);
        mmap.put("details", details);

        // 计算总重量和总体积
        BigDecimal totalWeight = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;

        for (SalesOrderDetail item : details) {
            if (item.getTotalWeight() != null) {
                totalWeight = totalWeight.add(item.getTotalWeight());
            }
            if (item.getTotalVolume() != null) {
                totalVolume = totalVolume.add(item.getTotalVolume());
            }
        }

        mmap.put("totalWeight", totalWeight);
        mmap.put("totalVolume", totalVolume);

        return prefix + "/detail";
    }

}