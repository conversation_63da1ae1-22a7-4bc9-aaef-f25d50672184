<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增销售订单')" />
    <th:block th:include="include :: select2-css" />
    <style>
        /* 自定义列宽样式 */
        .col-product {
            width: 23%;
        }
        .col-sku {
            width: 23%;
        }
        .col-number {
            width: 8%;
        }
        .col-stock {
            width: 8%;
        }
        .col-price {
            width: 8%;
        }
        .col-discount {
            width: 8%;
        }
        .col-amount {
            width: 8%;
        }
        .col-action {
            width: 8%;
        }
        .col-select {
            width: 6%;
        }
    </style>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-salesOrder-add">
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户：</label>
            <div class="col-sm-8">
                <select id="customer-select" class="form-control" onchange="fillCustomerInfo()">
                    <option value="">请选择客户</option>
                </select>
                <input type="hidden" name="customerId" id="customerId">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户名称：</label>
            <div class="col-sm-8">
                <input name="customerName" id="customerName" class="form-control" type="text" required readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">联系电话：</label>
            <div class="col-sm-8">
                <input name="customerPhone" id="customerPhone" class="form-control" type="text" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">客户地址：</label>
            <div class="col-sm-8">
                <input name="customerAddress" id="customerAddress" class="form-control" type="text" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">订单类型：</label>
            <div class="col-sm-8">
                <select name="orderType" class="form-control">
                    <option value="0">普通</option>
                    <option value="1">预定</option>
                    <option value="2">印花</option>
                    <option value="3">定制</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">支付方式：</label>
            <div class="col-sm-8">
                <select name="paymentMethod" class="form-control">
                    <option value="现金">现金</option>
                    <option value="微信">微信</option>
                    <option value="支付宝">支付宝</option>
                    <option value="银行转账">银行转账</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="remark" class="form-control"></textarea>
            </div>
        </div>

        <!-- 商品组合快捷选择区域 -->
        <div class="form-group">
            <label class="col-sm-3 control-label">商品组合快速添加：</label>
            <div class="col-sm-8">
                <div class="input-group">
                    <select id="combo-select" class="form-control">
                        <option value="">请选择商品组合</option>
                    </select>
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-primary" onclick="addComboItems()">
                            <i class="fa fa-plus"></i> 一键添加组合
                        </button>
                    </span>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label">订单明细：</label>
            <div class="col-sm-8">
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="addDetail()">添加商品</button>
                    <!-- 保存为商品组合按钮 -->
                    <button type="button" class="btn btn-info" onclick="saveAsProductCombo()">
                        <i class="fa fa-save"></i> 保存为商品组合
                    </button>
                </div>
            </div>
        </div>

        <div class="form-group">
            <div class="col-sm-12">
                <table class="table table-bordered" id="detail-table">
                    <thead>
                    <tr>
                        <th class="col-select">
                            <input type="checkbox" id="select-all-checkbox" onclick="toggleAllCheckboxes(this)">
                        </th>
                        <th class="col-product">商品</th>
                        <th class="col-sku">SKU</th>
                        <th class="col-number">数量</th>
                        <th class="col-stock">可用库存</th>
                        <th class="col-price">单价(元)</th>
                        <th class="col-discount">优惠(元)</th>
                        <th class="col-amount">金额(元)</th>
                        <th class="col-action">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <!-- 订单明细行将在这里动态添加 -->
                    </tbody>
                    <tfoot>
                    <tr>
                        <td colspan="7" class="text-right"><strong>订单总金额：</strong></td>
                        <td colspan="2"><span id="total-amount">0.00</span>元</td>
                    </tr>
                    <tr>
                        <td colspan="7" class="text-right"><strong>优惠总金额：</strong></td>
                        <td colspan="2"><span id="total-discount">0.00</span>元</td>
                    </tr>
                    <tr>
                        <td colspan="7" class="text-right"><strong>实付总金额：</strong></td>
                        <td colspan="2"><span id="actual-amount">0.00</span>元</td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </form>
</div>

<!-- 新增商品组合对话框 -->
<div class="modal fade" id="saveComboModal" tabindex="-1" role="dialog" aria-labelledby="saveComboModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="saveComboModalLabel">保存为商品组合</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal m" id="form-save-combo">
                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">组合名称：</label>
                        <div class="col-sm-8">
                            <input name="comboName" id="comboName" class="form-control" type="text" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">状态：</label>
                        <div class="col-sm-8">
                            <div class="radio-box">
                                <input type="radio" name="status" value="1" checked>
                                <label for="status">启用</label>
                            </div>
                            <div class="radio-box">
                                <input type="radio" name="status" value="0">
                                <label for="status">禁用</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">备注说明：</label>
                        <div class="col-sm-8">
                            <textarea name="remark" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">已选商品：</label>
                        <div class="col-sm-8">
                            <div class="selected-items-container" id="selected-items-summary">
                                <p class="text-muted">未选择任何商品</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="submitSaveCombo()">保存</button>
            </div>
        </div>
    </div>
</div>
<div class="form-group">
    <div class="col-sm-12 text-center">
        <button type="button" class="btn btn-primary" onclick="addDetail()">添加商品</button>
        <!-- 保存为商品组合按钮 -->
        <button type="button" class="btn btn-info" onclick="saveAsProductCombo()">
            <i class="fa fa-save"></i> 保存为商品组合
        </button>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: select2-js" />
<script th:inline="javascript">
    var prefix = ctx + "youpo/salesOrder";
    var comboPrefix = ctx + "youpo/productCombo";
    var rowIndex = 0;
    // 用于存储已添加的SKU ID，防止重复添加
    var addedSkuIds = [];
    // 用于存储SKU的库存信息
    var stockInfo = {};
    // 用于调试
    var debugInfo = {
        addedRows: []
    };

    // 初始化客户下拉框和商品组合下拉框
    $(function() {
        initCustomerSelect();
        initComboSelect();
    });

    // 初始化客户下拉框
    function initCustomerSelect() {
        $("#customer-select").select2({
            placeholder: "请选择客户",
            allowClear: true,
            ajax: {
                url: prefix + "/searchCustomers",
                dataType: 'json',
                delay: 250,
                method: 'POST',
                data: function (params) {
                    return {
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        options.push({
                            id: item.customerId,
                            text: (item.customerOwner?item.customerOwner:'')+(item.province?item.province:'')+item.customerName ,
                            customer: item
                        });
                    });
                    return {
                        results: options
                    };
                },
                cache: true
            }
        });
    }

    // 初始化商品组合下拉框
    function initComboSelect() {
        $("#combo-select").select2({
            placeholder: "请选择商品组合",
            allowClear: true,
            ajax: {
                url: prefix + "/searchProductCombos",
                dataType: 'json',
                delay: 250,
                method: 'POST',
                data: function (params) {
                    return {
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        options.push({
                            id: item.comboId,
                            text: item.comboName
                        });
                    });
                    return {
                        results: options
                    };
                },
                cache: true
            }
        });
    }

    // 全选/取消全选复选框
    function toggleAllCheckboxes(source) {
        var checkboxes = document.querySelectorAll('input[name="combo-item-select"]');
        for(var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = source.checked;
        }
    }

    // 填充客户信息
    function fillCustomerInfo() {
        var selected = $("#customer-select").select2('data')[0];
        if (selected && selected.customer) {
            $("#customerId").val(selected.customer.customerId);
            $("#customerName").val(selected.customer.customerName);
            $("#customerPhone").val(selected.customer.customerPhone);
            $("#customerAddress").val(selected.customer.customerAddress);
        } else {
            $("#customerId").val("");
            $("#customerName").val("");
            $("#customerPhone").val("");
            $("#customerAddress").val("");
        }
    }

    $("#form-salesOrder-add").validate({
        focusCleanup: true,
        rules: {
            customerName: {
                required: true
            }
        }
    });

    // 添加商品组合中的所有项目
    function addComboItems() {
        var comboId = $("#combo-select").val();
        if (!comboId) {
            $.modal.alertWarning("请先选择商品组合");
            return;
        }

        // 获取组合项目详情
        $.ajax({
            url: prefix + "/getProductComboItems/" + comboId,
            type: "get",
            success: function(result) {
                if (result.code == 0 && result.data && result.data.length > 0) {
                    // 保存原始添加行的信息
                    var rows = result.data;
                    console.log("组合项目数据:", rows);

                    // 批量添加商品/SKU
                    $.each(rows, function(index, item) {
                        if (item.item_type == 1) {
                            // 商品类型，添加商品行
                            addProductFromCombo(item);
                        } else if (item.item_type == 2) {
                            // SKU类型，添加SKU行
                            addSkuFromCombo(item);
                        }
                    });

                    // 清空组合选择框
                    $("#combo-select").val(null).trigger('change');
                    $.modal.msgSuccess("已成功添加组合商品");
                } else {
                    $.modal.msgWarning("此商品组合不包含任何商品");
                }
            },
            error: function() {
                $.modal.msgError("获取组合项目失败");
            }
        });
    }

    // 从组合中添加商品
    function addProductFromCombo(item) {
        // 构造一个新行
        var newRowIndex = addDetail();
        console.log("添加商品，当前行索引:", newRowIndex, "商品ID:", item.ref_id);

        // 获取商品信息并设置
        $.ajax({
            url: prefix + "/searchProducts",
            type: "post",
            data: {
                keyword: ""
            },
            success: function(result) {
                if (result.code == 0 && result.data) {
                    var products = result.data;
                    for(var i = 0; i < products.length; i++) {
                        if(products[i].productId == item.ref_id) {
                            // 找到匹配的商品
                            var product = products[i];
                            console.log("找到匹配商品:", product);

                            // 创建商品选项并选中
                            var productOption = new Option(
                                product.productName ,
                                product.productId,
                                true,
                                true
                            );

                            // 添加选项并触发change事件
                            $("#product-" + newRowIndex).append(productOption).trigger('change');

                            // 设置数量
                            $("#quantity-" + newRowIndex).val(item.quantity);

                            // 添加到调试信息
                            debugInfo.addedRows.push({
                                type: 'product',
                                rowIndex: newRowIndex,
                                productId: product.productId,
                                productName: product.productName
                            });

                            break;
                        }
                    }
                }
            }
        });
    }

    // 修复后的从组合中添加SKU函数
    function addSkuFromCombo(item) {
        console.log("添加SKU，SKU ID:", item.ref_id);

        // 首先查询需要添加的SKU详细信息
        $.ajax({
            url: prefix + "/searchSkus",
            type: "post",
            data: {
                keyword: ""
            },
            success: function(skuResult) {
                if (skuResult.code == 0 && skuResult.data) {
                    var skus = skuResult.data;
                    var targetSku = null;

                    // 查找组合中指定的SKU
                    for(var i = 0; i < skus.length; i++) {
                        if(skus[i].skuId == item.ref_id) {
                            targetSku = skus[i];
                            break;
                        }
                    }

                    if(targetSku) {
                        console.log("找到匹配SKU:", targetSku);

                        // 检查是否已添加
                        if(addedSkuIds.indexOf(parseInt(targetSku.skuId)) > -1) {
                            console.log("SKU已存在，跳过:", targetSku.skuId);
                            return;
                        }

                        // 获取对应的商品ID
                        var productId = targetSku.productId;

                        // 添加一个标准行
                        var currentIndex = addDetail();
                        console.log("为SKU创建新行，行索引:", currentIndex);

                        // 获取商品信息
                        $.ajax({
                            url: prefix + "/searchProducts",
                            type: "post",
                            data: {
                                keyword: ""
                            },
                            success: function(productResult) {
                                if (productResult.code == 0 && productResult.data) {
                                    var products = productResult.data;
                                    var targetProduct = null;

                                    // 查找商品
                                    for(var i = 0; i < products.length; i++) {
                                        if(products[i].productId == productId) {
                                            targetProduct = products[i];
                                            break;
                                        }
                                    }

                                    if(targetProduct) {
                                        console.log("找到SKU对应的商品:", targetProduct);

                                        // 步骤1: 手动创建商品选项并选中
                                        var productText = targetProduct.productName ;

                                        // 确保选择器已初始化并且正确设置data-row属性
                                        $("#product-" + currentIndex).data("row", currentIndex);

                                        // 销毁并重建商品选择器
                                        if ($("#product-" + currentIndex).hasClass("select2-hidden-accessible")) {
                                            $("#product-" + currentIndex).select2('destroy');
                                        }

                                        $("#product-" + currentIndex).empty();
                                        var newOption = new Option(productText, targetProduct.productId, true, true);
                                        $("#product-" + currentIndex).append(newOption);
                                        $("#product-" + currentIndex).val(targetProduct.productId);

                                        // 重新初始化select2，并确保带上change事件处理
                                        $("#product-" + currentIndex).select2({
                                            data: [{
                                                id: targetProduct.productId,
                                                text: productText
                                            }]
                                        }).on("change", function() {
                                            const rowIdx = $(this).data("row");
                                            loadSkus(rowIdx);
                                        });

                                        // 步骤2: 手动加载SKU选项
                                        console.log("开始加载SKU选项");

                                        // 确保SKU选择器已初始化并且正确设置data-row属性
                                        $("#sku-" + currentIndex).data("row", currentIndex);

                                        // 销毁并重建SKU选择器
                                        if ($("#sku-" + currentIndex).hasClass("select2-hidden-accessible")) {
                                            $("#sku-" + currentIndex).select2('destroy');
                                        }

                                        // 准备SKU数据
                                        var skuText = targetSku.color + ' ' + targetSku.size;

                                        // 清空选项并添加我们的SKU
                                        $("#sku-" + currentIndex).empty();
                                        var skuOption = new Option(skuText, targetSku.skuId, true, true);
                                        $("#sku-" + currentIndex).append(skuOption);
                                        $("#sku-" + currentIndex).val(targetSku.skuId);

                                        // 重新初始化select2，并确保带上change事件处理
                                        $("#sku-" + currentIndex).select2({
                                            data: [{
                                                id: targetSku.skuId,
                                                text: skuText,
                                                salePrice: targetSku.salePrice,
                                                stock: targetSku.availableStock
                                            }]
                                        }).on('change', function() {
                                            const rowIdx = $(this).data("row");
                                            loadSkuInfo(rowIdx);
                                        });

                                        console.log("SKU选项加载完成");

                                        // 步骤3: 设置其他相关数据
                                        // 设置库存信息
                                        stockInfo[targetSku.skuId] = targetSku.availableStock;
                                        $("#available-stock-" + currentIndex).text(targetSku.availableStock);

                                        // 设置单价
                                        $("#unit-price-" + currentIndex).val(targetSku.salePrice || 0);

                                        // 设置数量（确保不超过库存）
                                        var quantity = item.quantity;
                                        $("#quantity-" + currentIndex).val(quantity);

                                        // 添加到已选SKU列表
                                        addedSkuIds.push(parseInt(targetSku.skuId));

                                        // 计算金额
                                        calculateAmount(currentIndex);

                                        // 添加到调试信息
                                        debugInfo.addedRows.push({
                                            type: 'sku',
                                            rowIndex: currentIndex,
                                            productId: targetProduct.productId,
                                            productName: targetProduct.productName,
                                            skuId: targetSku.skuId,
                                            skuInfo: targetSku.color + ' ' + targetSku.size
                                        });

                                        console.log("SKU添加完成，行索引:", currentIndex);
                                    } else {
                                        console.error("未找到SKU对应的商品:", productId);
                                    }
                                } else {
                                    console.error("获取商品数据失败");
                                }
                            },
                            error: function(err) {
                                console.error("获取商品数据出错:", err);
                            }
                        });
                    } else {
                        console.error("未找到匹配的SKU:", item.ref_id);
                    }
                } else {
                    console.error("获取SKU数据失败");
                }
            },
            error: function(err) {
                console.error("获取SKU数据出错:", err);
            }
        });
    }

    // 修复后的addDetail函数，添加data-row属性
    function addDetail() {
        var detailHtml = '<tr id="row-' + rowIndex + '">' +
            '<td>' +
            '<input type="checkbox" name="combo-item-select" class="combo-item-checkbox" value="' + rowIndex + '">' +
            '</td>' +
            '<td>' +
            '<select class="form-control product-select" id="product-' + rowIndex + '" data-row="' + rowIndex + '">' +
            '<option value="">请选择商品</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<select class="form-control sku-select" id="sku-' + rowIndex + '" name="skuIds[]" data-row="' + rowIndex + '">' +
            '<option value="">请选择SKU</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control quantity" id="quantity-' + rowIndex + '" name="quantities[]" value="1" min="1" data-row="' + rowIndex + '" onchange="checkQuantity(' + rowIndex + ')">' +
            '</td>' +
            '<td>' +
            '<span class="available-stock" id="available-stock-' + rowIndex + '">0</span>' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control unit-price" id="unit-price-' + rowIndex + '" name="unitPrices[]" value="0" min="0" step="0.01" data-row="' + rowIndex + '" onchange="calculateAmount(' + rowIndex + ')" readonly>' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control discount" id="discount-' + rowIndex + '" name="discounts[]" value="0" min="0" step="0.01" data-row="' + rowIndex + '" onchange="calculateAmount(' + rowIndex + ')">' +
            '</td>' +
            '<td>' +
            '<span class="row-amount" id="amount-' + rowIndex + '">0.00</span>' +
            '</td>' +
            '<td>' +
            '<a class="btn btn-danger btn-xs" onclick="removeDetail(' + rowIndex + ')"><i class="fa fa-remove"></i> 删除</a>' +
            '</td>' +
            '</tr>';

        $("#detail-table tbody").append(detailHtml);

        // 保存当前行索引
        var currentIndex = rowIndex;
        rowIndex++;

        // 初始化商品选择器
        initProductSelect(currentIndex);

        return currentIndex;
    }

    // 单独提取商品选择器初始化函数
    function initProductSelect(rowIndex) {
        $("#product-" + rowIndex).select2({
            placeholder: "请选择商品",
            allowClear: true,
            ajax: {
                url: prefix + "/searchProducts",
                dataType: 'json',
                delay: 250,
                method: 'POST',
                data: function (params) {
                    return {
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        options.push({
                            id: item.productId,
                            text: item.productName
                        });
                    });
                    return {
                        results: options
                    };
                },
                cache: true
            }
        }).on("change", function() {
            // 使用data属性获取行索引，避免闭包问题
            const rowIdx = $(this).data("row");
            loadSkus(rowIdx);
        });
    }

    // 修复后的loadSkus函数
    function loadSkus(rowIndex) {
        var productId = $("#product-" + rowIndex).val();
        if (!productId) {
            return;
        }

        console.log("加载SKU，行索引: " + rowIndex + ", 商品ID: " + productId);

        // 获取当前行已选择的SKU ID并从已添加列表中移除
        var currentSkuId = $("#sku-" + rowIndex).val();
        if (currentSkuId) {
            var currentSkuIdInt = parseInt(currentSkuId);
            console.log("准备移除SKU ID:", currentSkuIdInt, "当前addedSkuIds:", JSON.stringify(addedSkuIds));

            var index = addedSkuIds.indexOf(currentSkuIdInt);
            if (index > -1) {
                addedSkuIds.splice(index, 1);
                console.log("从已添加SKU列表中移除:", currentSkuIdInt, "移除后addedSkuIds:", JSON.stringify(addedSkuIds));
            }
            // 移除库存信息
            delete stockInfo[currentSkuId];
        }

        // 重新初始化SKU选择框
        if ($("#sku-" + rowIndex).hasClass("select2-hidden-accessible")) {
            $("#sku-" + rowIndex).select2('destroy');
        }

        // 重置SKU选择框
        $("#sku-" + rowIndex).empty().append('<option value="">请选择SKU</option>');

        // 重置数量为默认的1
        $("#quantity-" + rowIndex).val(1);

        // 重置相关数据
        $("#unit-price-" + rowIndex).val(0);
        $("#available-stock-" + rowIndex).text("0");
        calculateAmount(rowIndex);

        // 使用Select2的AJAX特性直接从服务器获取数据
        $("#sku-" + rowIndex).select2({
            placeholder: "请选择SKU",
            allowClear: true,
            ajax: {
                url: prefix + "/searchSkus",
                dataType: 'json',
                delay: 250,
                method: 'POST',
                data: function (params) {
                    return {
                        productId: productId,
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        // 只添加未被选择的SKU
                        if (addedSkuIds.indexOf(parseInt(item.skuId)) === -1) {
                            options.push({
                                id: item.skuId,
                                text: item.color + ' ' + item.size ,
                                salePrice: item.salePrice || 0,
                                stock: item.availableStock
                            });
                        }
                    });
                    return { results: options };
                },
                cache: true
            }
        }).on('change', function() {
            // 使用data属性获取行索引
            const rowIdx = $(this).data("row");
            loadSkuInfo(rowIdx);
        });
    }

    // 加载SKU信息
    function loadSkuInfo(rowIndex) {
        var skuSelect = $("#sku-" + rowIndex);
        var selectedSkuId = skuSelect.val();

        if (!selectedSkuId) {
            return; // 如果没有选择SKU，直接返回
        }

        // 将selectedSkuId转换为整数以确保一致比较
        var selectedSkuIdInt = parseInt(selectedSkuId);
        var selectedOption = skuSelect.select2('data')[0];

        // 用于调试 - 打印当前的addedSkuIds
        console.log("当前添加的SKU IDs:", JSON.stringify(addedSkuIds));
        console.log("正在添加SKU ID:", selectedSkuIdInt);

        if (selectedOption) {
            // 检查是否已经存在相同的SKU（检查完全匹配和确保不是同一行）
            var duplicateFound = false;

            // 遍历所有行查找重复
            $("#detail-table tbody tr").each(function() {
                var rowId = $(this).attr("id").replace("row-", "");
                // 跳过当前行
                if (rowId != rowIndex) {
                    var otherSkuId = $("#sku-" + rowId).val();
                    if (otherSkuId && parseInt(otherSkuId) === selectedSkuIdInt) {
                        duplicateFound = true;
                        return false; // 跳出循环
                    }
                }
            });

            if (duplicateFound) {
                $.modal.alertWarning("该SKU已经添加，不能重复添加");
                skuSelect.val("").trigger('change');
                return;
            }

            // 添加到已选SKU列表
            addedSkuIds.push(selectedSkuIdInt);
            console.log("添加后的SKU IDs:", JSON.stringify(addedSkuIds));

            // 存储SKU库存信息
            var availableStock = selectedOption.stock || 0;
            stockInfo[selectedSkuId] = availableStock;

            // 显示可用库存
            $("#available-stock-" + rowIndex).text(availableStock);

            // 设置单价
            $("#unit-price-" + rowIndex).val(selectedOption.salePrice || 0);

            // 确保数量不为负数
            var quantity = parseInt($("#quantity-" + rowIndex).val()) || 1;
            if (quantity < 1) {
                $("#quantity-" + rowIndex).val(1);
            }

            // 计算金额
            calculateAmount(rowIndex);
        }
    }

    function checkQuantity(rowIndex) {
        var quantity = parseInt($("#quantity-" + rowIndex).val()) || 0;

        // 数量不能小于1
        if (quantity < 1) {
            $("#quantity-" + rowIndex).val(1);
            quantity = 1;
        }

        // 重新计算金额
        calculateAmount(rowIndex);
    }

    function calculateAmount(rowIndex) {
        var quantity = parseFloat($("#quantity-" + rowIndex).val()) || 0;
        var unitPrice = parseFloat($("#unit-price-" + rowIndex).val()) || 0;
        var discount = parseFloat($("#discount-" + rowIndex).val()) || 0;

        var amount = quantity * unitPrice - discount;
        amount = amount < 0 ? 0 : amount;

        $("#amount-" + rowIndex).text(amount.toFixed(2));

        // 计算总金额
        calculateTotal();
    }

    function calculateTotal() {
        var totalAmount = 0;
        var totalDiscount = 0;

        // 计算所有行的总金额和优惠总额
        $(".row-amount").each(function() {
            totalAmount += parseFloat($(this).text()) || 0;
        });

        $(".discount").each(function() {
            totalDiscount += parseFloat($(this).val()) || 0;
        });

        var actualAmount = totalAmount;

        $("#total-amount").text(totalAmount.toFixed(2));
        $("#total-discount").text(totalDiscount.toFixed(2));
        $("#actual-amount").text(actualAmount.toFixed(2));
    }

    function removeDetail(rowIndex) {
        var skuId = $("#sku-" + rowIndex).val();
        // 从已添加SKU列表中移除
        if (skuId) {
            var index = addedSkuIds.indexOf(parseInt(skuId));
            if (index > -1) {
                addedSkuIds.splice(index, 1);
            }
            // 移除库存信息
            delete stockInfo[skuId];
        }

        $("#row-" + rowIndex).remove();
        calculateTotal();
    }

    // 保存为商品组合
    function saveAsProductCombo() {
        // 获取选中的复选框
        var checkedItems = $('input[name="combo-item-select"]:checked');

        if (checkedItems.length === 0) {
            $.modal.alertWarning("请至少选择一个商品或SKU后再保存为组合");
            return;
        }

        // 收集选中商品的信息
        var selectedItems = [];
        var summaryHtml = '';

        console.log("选中的商品数量:", checkedItems.length);

        checkedItems.each(function() {
            var rowIndex = $(this).val();
            var row = $("#row-" + rowIndex);
            var productId = row.find(".product-select").val();
            var skuId = row.find(".sku-select").val();
            var productName = "";

            try {
                productName = row.find(".product-select").select2('data')[0].text || '';
            } catch (e) {
                console.error("获取商品名称失败:", e);
            }

            console.log("处理行:", rowIndex, "商品ID:", productId, "SKU ID:", skuId);

            if (skuId) {
                // SKU类型
                var quantity = parseInt(row.find(".quantity").val()) || 1;
                var skuName = "";

                try {
                    skuName = row.find(".sku-select").select2('data')[0].text || '';
                } catch (e) {
                    console.error("获取SKU名称失败:", e);
                }

                selectedItems.push({
                    itemType: 2, // SKU类型
                    refId: skuId,
                    quantity: quantity,
                    name: productName + ' - ' + skuName
                });

                // 添加到摘要信息
                summaryHtml += '<div class="selected-item">' + productName + ' - ' + skuName + ' x ' + quantity + '</div>';
            } else if (productId) {
                // 商品类型 - 无SKU
                var quantity = parseInt(row.find(".quantity").val()) || 1;

                selectedItems.push({
                    itemType: 1, // 商品类型
                    refId: productId,
                    quantity: quantity,
                    name: productName
                });

                // 添加到摘要信息
                summaryHtml += '<div class="selected-item">' + productName + ' x ' + quantity + '</div>';
            }
        });

        console.log("选中的商品项:", selectedItems);

        if (selectedItems.length === 0) {
            $.modal.alertWarning("所选项目中没有有效的商品或SKU，请重新选择");
            return;
        }

        // 更新摘要信息
        if (summaryHtml !== '') {
            $("#selected-items-summary").html(summaryHtml);
        } else {
            $("#selected-items-summary").html('<p class="text-muted">未选择任何商品</p>');
        }

        // 打开保存为组合对话框
        $("#saveComboModal").modal("show");
    }

    // 提交保存商品组合
    function submitSaveCombo() {
        var comboName = $("#comboName").val();
        if (!comboName) {
            $.modal.alertWarning("请输入组合名称");
            return;
        }

        // 获取选中的复选框
        var checkedItems = $('input[name="combo-item-select"]:checked');
        var items = [];

        // 收集选中商品的信息
        checkedItems.each(function() {
            var rowIndex = $(this).val();
            var row = $("#row-" + rowIndex);
            var productId = row.find(".product-select").val();
            var skuId = row.find(".sku-select").val();

            if (skuId) {
                // SKU类型
                var quantity = parseInt(row.find(".quantity").val()) || 1;
                items.push({
                    itemType: 2, // SKU类型
                    refId: skuId,
                    quantity: quantity
                });
            } else if (productId) {
                // 商品类型 - 无SKU
                var quantity = parseInt(row.find(".quantity").val()) || 1;
                items.push({
                    itemType: 1, // 商品类型
                    refId: productId,
                    quantity: quantity
                });
            }
        });

        // 记录商品项数量
        console.log("提交的商品项数量:", items.length);

        // 构造保存组合的数据
        var comboData = {
            comboName: comboName,
            status: $("input[name='status']:checked").val(),
            remark: $("#form-save-combo textarea[name='remark']").val(),
            items: JSON.stringify(items)
        };

        // 发送请求保存商品组合
        $.ajax({
            url: comboPrefix + "/saveProductComboWithItems",
            type: "post",
            data: comboData,
            success: function(result) {
                if (result.code == 0) {
                    $.modal.msgSuccess("商品组合保存成功");
                    $("#saveComboModal").modal("hide");
                    // 重置表单
                    $("#form-save-combo")[0].reset();
                    $("#selected-items-summary").html('<p class="text-muted">未选择任何商品</p>');
                } else {
                    $.modal.alertError(result.msg || "保存失败");
                }
            },
            error: function() {
                $.modal.alertError("保存失败，请稍后重试");
            }
        });
    }

    function validateDetails() {
        // 验证客户是否已选择
        if (!$("#customerId").val()) {
            $.modal.alertWarning("请选择客户");
            return false;
        }

        if ($("#detail-table tbody tr").length === 0) {
            $.modal.alertWarning("请至少添加一个商品");
            return false;
        }

        var valid = true;
        $("#detail-table tbody tr").each(function() {
            var skuId = $(this).find(".sku-select").val();
            var quantity = parseInt($(this).find(".quantity").val());

            if (!skuId || !quantity || quantity <= 0) {
                valid = false;
                return false;
            }
        });

        if (!valid) {
            $.modal.alertWarning("订单明细填写不完整");
            return false;
        }

        return true;
    }

    function submitHandler() {
        if ($.validate.form() && validateDetails()) {
            $.operate.save(prefix + "/add", $('#form-salesOrder-add').serialize());
        }
    }
</script>
</body>
</html>