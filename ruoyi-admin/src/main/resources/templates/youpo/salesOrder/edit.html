<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改销售订单')" />
    <th:block th:include="include :: select2-css" />
    <style>
        /* 自定义列宽样式 */
        .col-product {
            width: 25%; /* 商品名称列加宽 */
        }
        .col-sku {
            width: 20%; /* SKU属性列加宽 */
        }
        .col-number {
            width: 8%; /* 数量列缩小 */
        }
        .col-stock {
            width: 5%;
        }
        .col-price {
            width: 10%;
        }
        .col-discount {
            width: 10%;
        }
        .col-amount {
            width: 10%;
        }
        .col-status {
            width: 8%;
        }
        .col-action {
            width: 8%;
        }

        /* 确保表格使用这些宽度设置 */
        #detail-table {
            table-layout: fixed;
            width: 100%;
        }

        #detail-table th,
        #detail-table td {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }
    </style>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-salesOrder-edit" th:object="${salesOrder}">
        <input name="orderId" th:field="*{orderId}" type="hidden">
        <div class="form-group">
            <label class="col-sm-3 control-label">订单编号：</label>
            <div class="col-sm-8">
                <input name="orderCode" th:field="*{orderCode}" class="form-control" type="text" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户：</label>
            <div class="col-sm-8">
                <input type="text" id="customer-display" class="form-control"
                       th:value="*{customerName} + (*{customerPhone} ? ' (' + *{customerPhone} + ')' : '')"
                       placeholder="点击选择客户" readonly onclick="openCustomerModal()" style="cursor: pointer;">
                <input type="hidden" name="customerId" id="customerId" th:value="*{customerId}">
                <input type="hidden" name="customerName" id="customerName" th:value="*{customerName}">
                <input type="hidden" name="customerPhone" id="customerPhone" th:value="*{customerPhone}">
                <input type="hidden" name="customerAddress" id="customerAddress" th:value="*{customerAddress}">
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label is-required">客户名称：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" th:value="*{customerName}" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">联系电话：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" th:value="*{customerPhone}" readonly>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">客户地址：</label>
            <div class="col-sm-8">
                <input class="form-control" type="text" th:value="*{customerAddress}" readonly>
            </div>
        </div>


        <div class="form-group">
            <label class="col-sm-3 control-label">订单类型：</label>
            <div class="col-sm-8">
                <select name="orderType" class="form-control" th:field="*{orderType}">
                    <option value="0">普通</option>
                    <option value="1">预定</option>
                    <option value="2">印花</option>
                    <option value="3">定制</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">支付方式：</label>
            <div class="col-sm-8">
                <select name="paymentMethod" class="form-control">
                    <option value="现金" th:selected="*{paymentMethod} == '现金'">现金</option>
                    <option value="微信" th:selected="*{paymentMethod} == '微信'">微信</option>
                    <option value="支付宝" th:selected="*{paymentMethod} == '支付宝'">支付宝</option>
                    <option value="银行转账" th:selected="*{paymentMethod} == '银行转账'">银行转账</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">订单状态：</label>
            <div class="col-sm-8">
                <select name="status" class="form-control" th:field="*{status}">
                    <option value="0">草稿</option>
                    <option value="1">待支付</option>
                    <option value="2">已支付</option>
                    <option value="3">部分发货</option>
                    <option value="4">已发货</option>
                    <option value="5">已完成</option>
                    <option value="6">已取消</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-3 control-label">备注：</label>
            <div class="col-sm-8">
                <textarea name="remark" class="form-control" th:text="*{remark}"></textarea>
            </div>
        </div>
        <!-- 商品组合快捷选择区域 -->
        <div class="form-group">
            <label class="col-sm-3 control-label">商品组合快速添加：</label>
            <div class="col-sm-8">
                <div class="input-group">
                    <select id="combo-select" class="form-control">
                        <option value="">请选择商品组合</option>
                    </select>
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-primary" onclick="addComboItems()">
                            <i class="fa fa-plus"></i> 一键添加组合
                        </button>
                    </span>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-sm-3 control-label">订单明细：</label>
            <div class="col-sm-8">
                <button type="button" class="btn btn-primary" onclick="addNewDetail()">添加商品</button>
                <!-- 保存为商品组合按钮 -->
                <button type="button" class="btn btn-info" onclick="saveAsProductCombo()">
                    <i class="fa fa-save"></i> 保存为商品组合
                </button>
            </div>
        </div>

        <!-- 用于存储要删除的detailId -->
        <div id="deleted-details-container"></div>

        <div class="form-group">
            <div class="col-sm-12">
                <table class="table table-bordered" id="detail-table">
                    <thead>
                    <tr>
                        <th class="col-checkbox">
                            <input type="checkbox" onchange="toggleAllCheckboxes(this)" title="全选/取消全选">
                        </th>
                        <th class="col-product">商品名称</th>
                        <th class="col-sku">SKU属性</th>
                        <th class="col-number">数量</th>
                        <th class="col-stock">可用库存</th>
                        <th class="col-price">单价(元)</th>
                        <th class="col-discount">优惠(元)</th>
                        <th class="col-amount">金额(元)</th>
                        <th class="col-status">出库状态</th>
                        <th class="col-action">操作</th>
                    </tr>
                    </thead>
                    <tbody id="existing-details">
                    <!-- 已有订单明细 -->
                    <tr th:each="detail, stat : ${details}" th:id="'existing-row-' + ${stat.index}">
                        <input type="hidden" class="existing-detail-id" th:value="${detail.detailId}" />
                        <input type="hidden" class="existing-sku-id" th:value="${detail.skuId}" />
                        <input type="hidden" class="existing-unit-price" th:value="${detail.unitPrice}" />
                        <input type="hidden" class="existing-product-id" th:value="${detail.remark}" />
                        <input type="hidden" class="existing-product-name" th:value="${detail.productName}" />
                        <input type="hidden" class="existing-sku-properties" th:value="${detail.skuProperties}" />
                        <td>
                            <input type="checkbox" name="combo-item-select" th:value="'existing-' + ${stat.index}">
                        </td>
                        <td>
                            <select class="form-control existing-product-select" th:id="'existing-product-' + ${stat.index}" th:data-row="${stat.index}" th:data-detail-id="${detail.detailId}">
                                <option th:value="${detail.remark}" th:text="${detail.productName}" selected></option>
                            </select>
                        </td>
                        <td>
                            <select class="form-control existing-sku-select" th:id="'existing-sku-' + ${stat.index}" th:data-row="${stat.index}" th:data-detail-id="${detail.detailId}">
                                <option th:value="${detail.skuId}" th:text="${detail.skuProperties}" selected></option>
                            </select>
                        </td>
                        <td>
                            <input type="number" class="form-control quantity existing-quantity"
                                   th:name="'existingQuantities[' + ${detail.detailId} + ']'"
                                   th:value="${detail.modifiedQuantity}"
                                   th:data-original-quantity="${detail.modifiedQuantity}"
                                   th:data-sku-id="${detail.skuId}"
                                   th:data-detail-id="${detail.detailId}"
                                   onchange="calculateExistingRowAmount(this)"
                                   th:readonly="${detail.shipmentStatus != 0}" style="width: 100%;">
                        </td>
                        <td>
                            <span class="existing-stock" th:id="'existing-stock-' + ${detail.skuId}">
                                加载中...
                            </span>
                        </td>
                        <td th:text="${detail.unitPrice}"></td>
                        <td>
                            <input type="number" class="form-control existing-discount"
                                   th:name="'existingDiscounts[' + ${detail.detailId} + ']'"
                                   th:value="${detail.discountAmount}"
                                   onchange="calculateExistingRowAmount(this)"
                                   step="0.01" min="0" style="width: 100%;">
                        </td>
                        <td>
                            <span class="existing-amount" th:id="'existing-amount-' + ${detail.detailId}" th:text="${#numbers.formatDecimal(detail.actualAmount, 1, 2)}"></span>
                        </td>
                        <td>
                            <span th:if="${detail.shipmentStatus == 0}" class="badge badge-primary">未出库</span>
                            <span th:if="${detail.shipmentStatus == 1}" class="badge badge-warning">部分出库</span>
                            <span th:if="${detail.shipmentStatus == 2}" class="badge badge-success">已出库</span>
                        </td>
                        <td style="white-space: nowrap;">
                            <a class="btn btn-primary btn-sm" href="javascript:void(0)"
                               th:onclick="'insertDetailAfterExisting(' + ${stat.index} + ')'"
                               style="margin-right: 3px;">
                                <i class="fa fa-plus"></i> 添加
                            </a><a class="btn btn-danger" href="javascript:void(0)"
                               th:if="${detail.shipmentStatus == 0}"
                               th:onclick="'removeExistingDetail(' + ${stat.index} + ',' + ${detail.detailId} + ',' + ${detail.skuId} + ')'"
                               style="font-size: 10px; padding: 2px 6px;">
                                <i class="fa fa-remove"></i>
                            </a>
                        </td>
                    </tr>
                    </tbody>
                    <tbody id="new-details">
                    <!-- 新添加的订单明细将在这里动态添加 -->
                    </tbody>
                    <tfoot>
                    <tr>
                        <td colspan="7" class="text-right"><strong>订单总金额：</strong></td>
                        <td colspan="3"><span id="total-amount" th:text="${#numbers.formatDecimal(salesOrder.orderAmount, 1, 2)}">0.00</span>元</td>
                    </tr>
                    <tr>
                        <td colspan="7" class="text-right"><strong>优惠总金额：</strong></td>
                        <td colspan="3"><span id="total-discount" th:text="${#numbers.formatDecimal(salesOrder.discountAmount, 1, 2)}">0.00</span>元</td>
                    </tr>
                    <tr>
                        <td colspan="7" class="text-right"><strong>实付总金额：</strong></td>
                        <td colspan="3"><span id="actual-amount" th:text="${#numbers.formatDecimal(salesOrder.actualAmount, 1, 2)}">0.00</span>元</td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </form>
</div>

<!-- 客户选择模态框 -->
<div class="modal fade" id="customerModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">选择客户</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <input type="text" id="customer-search" class="form-control" placeholder="输入客户名称搜索">
                </div>
                <div id="customer-list" style="max-height: 400px; overflow-y: auto;">
                    <!-- 客户列表将在这里动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 保存为商品组合的模态框 -->
<div class="modal fade" id="saveComboModal" tabindex="-1" role="dialog" aria-labelledby="saveComboModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="saveComboModalLabel">保存为商品组合</h4>
            </div>
            <div class="modal-body">
                <form id="form-save-combo">
                    <div class="form-group">
                        <label for="comboName">组合名称：</label>
                        <input type="text" class="form-control" id="comboName" name="comboName" required>
                    </div>
                    <div class="form-group">
                        <label>状态：</label>
                        <div>
                            <label class="radio-inline">
                                <input type="radio" name="status" value="1" checked> 启用
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="status" value="0"> 禁用
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="remark">备注：</label>
                        <textarea class="form-control" name="remark" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>选中的商品：</label>
                        <div id="selected-items-summary">
                            <p class="text-muted">未选择任何商品</p>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="submitSaveCombo()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 在编辑页表单底部添加打印按钮 - 改为独立按钮 -->
<div class="form-group">
    <div class="col-sm-12 text-center">
        <button type="button" class="btn btn-primary" onclick="addNewDetail()">添加商品</button>
        <button type="button" class="btn btn-info" onclick="printOrderWithPrice()"><i class="fa fa-print"></i> 订单打印</button>
        <button type="button" class="btn btn-warning" onclick="printOutbound()"><i class="fa fa-truck"></i> 出库打印</button>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: select2-js" />
<script th:inline="javascript">
    var prefix = ctx + "youpo/salesOrder";
    var comboPrefix = ctx + "youpo/productCombo";
    var rowIndex = 0;
    // 用于存储已添加的SKU ID，防止重复添加
    var addedSkuIds = [];
    // 用于存储被删除的detailId
    var deletedDetailIds = [];
    // 用于存储SKU库存信息
    var stockInfo = {};
    // 用于存储已有SKU的当前数量
    var existingSkuQuantity = {};
    // 存储对应的detailId和skuId的映射
    var detailToSkuMap = {};
    // 存储SKU的系统库存
    var systemStockInfo = {};

    // 初始化已有的SKU ID列表和获取库存信息
    $(function() {
        // 初始化客户搜索功能
        $("#customer-search").on('input', function() {
            var keyword = $(this).val();
            searchCustomers(keyword);
        });

        // 页面加载时检查客户ID
        console.log('页面加载时的客户ID:', $("#customerId").val());
        console.log('页面加载时的客户名称:', $("#customerName").val());

        // 如果客户ID为空，尝试从其他地方获取
        if (!$("#customerId").val()) {
            console.log('客户ID为空，尝试从表单对象获取');
            // 尝试从表单的data属性或其他地方获取
            var formData = $("#form-salesOrder-edit").serializeArray();
            console.log('表单数据:', formData);

            // 查找表单数据中的customerId
            var customerIdFromForm = null;
            $.each(formData, function(index, field) {
                if (field.name === 'customerId') {
                    customerIdFromForm = field.value;
                }
            });
            console.log('从表单数据中找到的customerId:', customerIdFromForm);

            // 如果表单数据中有customerId，则设置到隐藏字段
            if (customerIdFromForm) {
                $("#customerId").val(customerIdFromForm);
                console.log('已设置customerId为:', customerIdFromForm);
            } else {
                // 尝试从页面中的其他地方获取客户ID
                console.log('尝试从页面元素中查找客户ID');

                // 检查是否有其他包含客户ID的元素
                var allInputs = $("input").map(function() {
                    return {
                        name: this.name,
                        id: this.id,
                        value: this.value,
                        type: this.type
                    };
                }).get();
                console.log('所有input元素:', allInputs);

                // 尝试从 URL 中获取订单ID，然后通过AJAX获取客户ID
                var url = window.location.href;
                var orderIdMatch = url.match(/\/edit\/(\d+)/);
                if (orderIdMatch) {
                    var orderId = orderIdMatch[1];
                    console.log('从 URL 中获取到订单ID:', orderId);

                    // 通过AJAX获取订单信息
                    console.log('发送AJAX请求到:', prefix + '/getOrderInfo/' + orderId);
                    $.ajax({
                        url: prefix + '/getOrderInfo/' + orderId,
                        type: 'GET',
                        success: function(result) {
                            console.log('AJAX返回结果:', result);
                            if (result.code == 0 && result.data) {
                                console.log('订单数据:', result.data);
                                console.log('订单数据的所有属性:', Object.keys(result.data));
                                console.log('检查customerId字段:', result.data.customerId);
                                console.log('检查customer_id字段:', result.data.customer_id);
                                console.log('检查customerName字段:', result.data.customerName);

                                // 尝试多种可能的字段名
                                var customerId = result.data.customerId || result.data.customer_id || result.data.customerid;
                                if (customerId) {
                                    console.log('通过AJAX获取到的客户ID:', customerId);
                                    $("#customerId").val(customerId);
                                    $("#customerName").val(result.data.customerName);
                                    $("#customerPhone").val(result.data.customerPhone);
                                    $("#customerAddress").val(result.data.customerAddress);

                                    // 更新显示框
                                    var displayText = result.data.customerName;
                                    if (result.data.customerPhone) {
                                        displayText += ' (' + result.data.customerPhone + ')';
                                    }
                                    $("#customer-display").val(displayText);

                                    console.log('已设置customerId为:', customerId);
                                } else {
                                    console.log('订单数据中没有customerId，尝试通过客户名称查找');

                                    // 如果有客户名称，尝试通过客户名称查找客户ID
                                    if (result.data.customerName) {
                                        console.log('尝试通过客户名称查找客户ID:', result.data.customerName);

                                        $.ajax({
                                            url: prefix + '/searchCustomers',
                                            type: 'POST',
                                            data: {
                                                keyword: result.data.customerName
                                            },
                                            success: function(customerResult) {
                                                console.log('客户搜索结果:', customerResult);
                                                if (customerResult.code == 0 && customerResult.data && customerResult.data.length > 0) {
                                                    // 查找匹配的客户
                                                    var matchedCustomer = null;
                                                    for (var i = 0; i < customerResult.data.length; i++) {
                                                        var customer = customerResult.data[i];
                                                        if (customer.customerName === result.data.customerName) {
                                                            matchedCustomer = customer;
                                                            break;
                                                        }
                                                    }

                                                    if (matchedCustomer) {
                                                        console.log('找到匹配的客户:', matchedCustomer);
                                                        $("#customerId").val(matchedCustomer.customerId);
                                                        console.log('已设置customerId为:', matchedCustomer.customerId);

                                                        // 更新显示框
                                                        var displayText = matchedCustomer.customerName;
                                                        if (matchedCustomer.customerPhone) {
                                                            displayText += ' (' + matchedCustomer.customerPhone + ')';
                                                        }
                                                        $("#customer-display").val(displayText);
                                                    } else {
                                                        console.log('未找到匹配的客户');
                                                        // 作为最后的手段，设置一个默认值
                                                        $("#customerId").val('1'); // 设置一个默认的客户ID
                                                        console.log('设置默认customerId为: 1');
                                                    }
                                                } else {
                                                    console.log('客户搜索没有结果');
                                                    // 作为最后的手段，设置一个默认值
                                                    $("#customerId").val('1'); // 设置一个默认的客户ID
                                                    console.log('设置默认customerId为: 1');
                                                }
                                            },
                                            error: function() {
                                                console.log('客户搜索失败');
                                                // 作为最后的手段，设置一个默认值
                                                $("#customerId").val('1'); // 设置一个默认的客户ID
                                                console.log('设置默认customerId为: 1');
                                            }
                                        });
                                    } else {
                                        console.log('订单数据中也没有客户名称');
                                        // 作为最后的手段，设置一个默认值
                                        $("#customerId").val('1'); // 设置一个默认的客户ID
                                        console.log('设置默认customerId为: 1');
                                    }
                                }
                            } else {
                                console.log('AJAX返回的数据格式不正确或没有数据');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log('AJAX获取订单信息失败:', status, error);
                            console.log('xhr:', xhr);
                        }
                    });
                }
            }
        }

        // 初始化商品组合下拉框
        initComboSelect();

        // 初始化已有明细的商品和SKU下拉框
        $(".existing-product-select").each(function() {
            var rowIndex = $(this).data("row");
            initExistingProductSelect(rowIndex);
        });

        $(".existing-sku-select").each(function() {
            var rowIndex = $(this).data("row");
            initExistingSkuSelect(rowIndex);
        });

        // 获取所有已有的SKU ID
        var skuIds = [];
        $(".existing-quantity").each(function() {
            var skuId = parseInt($(this).data("sku-id"));
            var detailId = $(this).data("detail-id");
            var originalQuantity = parseInt($(this).data("original-quantity"));

            skuIds.push(skuId);
            addedSkuIds.push(skuId);
            detailToSkuMap[detailId] = skuId;
            existingSkuQuantity[skuId] = originalQuantity;
        });

        // 一次性批量获取所有SKU的库存信息
        if (skuIds.length > 0) {
            $.ajax({
                url: prefix + "/searchSkus",
                type: "POST",
                data: {
                    keyword: ""
                },
                success: function(data) {
                    if (data.code == 0 && data.data) {
                        // 处理每个SKU
                        for (var i = 0; i < data.data.length; i++) {
                            var sku = data.data[i];
                            var skuId = sku.skuId;

                            // 如果是我们需要的SKU
                            if (addedSkuIds.indexOf(parseInt(skuId)) >= 0) {
                                // 存储系统库存信息
                                systemStockInfo[skuId] = parseInt(sku.availableStock);

                                // 获取订单中该SKU的已锁定数量
                                var lockedQuantity = existingSkuQuantity[skuId] || 0;

                                // 计算实际可用库存 = 系统可用库存 + 当前订单锁定的库存
                                var actualAvailable = parseInt(sku.availableStock) + lockedQuantity;

                                // 更新库存显示
                                $("#existing-stock-" + skuId).text(actualAvailable);

                                // 存储库存信息
                                stockInfo[skuId] = actualAvailable;
                            }
                        }
                    }
                }
            });
        }

        // 页面加载完成后计算一次总金额
        calculateTotal();
    });

    // 打开客户选择模态框
    function openCustomerModal() {
        $("#customerModal").modal('show');
        // 初始加载客户列表
        searchCustomers('');
    }

    // 搜索客户
    function searchCustomers(keyword) {
        $.ajax({
            url: prefix + "/searchCustomers",
            type: "POST",
            data: {
                keyword: keyword
            },
            success: function(result) {
                if (result.code == 0 && result.data) {
                    var html = '';
                    $.each(result.data, function(index, customer) {
                        var customerText = customer.customerName;
                        if (customer.customerPhone) {
                            customerText += ' (' + customer.customerPhone + ')';
                        }
                        if (customer.customerOwner) {
                            customerText = customer.customerOwner + ' - ' + customerText;
                        }
                        if (customer.province) {
                            customerText = customer.province + ' ' + customerText;
                        }

                        // 使用data属性来避免字符串转义问题
                        html += '<div class="customer-item" style="padding: 10px; border-bottom: 1px solid #eee; cursor: pointer;" '
                        html += 'data-customer-id="' + customer.customerId + '" '
                        html += 'data-customer-name="' + customer.customerName.replace(/"/g, '&quot;') + '" '
                        html += 'data-customer-phone="' + (customer.customerPhone || '').replace(/"/g, '&quot;') + '" '
                        html += 'data-customer-address="' + (customer.customerAddress || '').replace(/"/g, '&quot;') + '" '
                        html += 'onclick="selectCustomerFromData(this)">'
                        html += '<strong>' + customerText + '</strong>';
                        if (customer.customerAddress) {
                            html += '<br><small class="text-muted">' + customer.customerAddress + '</small>';
                        }
                        html += '</div>';
                    });

                    if (html === '') {
                        html = '<div class="text-center text-muted" style="padding: 20px;">没有找到匹配的客户</div>';
                    }

                    $("#customer-list").html(html);
                } else {
                    $("#customer-list").html('<div class="text-center text-muted" style="padding: 20px;">加载失败</div>');
                }
            },
            error: function() {
                $("#customer-list").html('<div class="text-center text-muted" style="padding: 20px;">加载失败</div>');
            }
        });
    }

    // 从数据属性选择客户
    function selectCustomerFromData(element) {
        var customerId = $(element).data('customer-id');
        var customerName = $(element).data('customer-name');
        var customerPhone = $(element).data('customer-phone');
        var customerAddress = $(element).data('customer-address');

        selectCustomer(customerId, customerName, customerPhone, customerAddress);
    }

    // 选择客户
    function selectCustomer(customerId, customerName, customerPhone, customerAddress) {
        $("#customerId").val(customerId);
        $("#customerName").val(customerName);
        $("#customerPhone").val(customerPhone);
        $("#customerAddress").val(customerAddress);

        // 更新显示框
        var displayText = customerName;
        if (customerPhone) {
            displayText += ' (' + customerPhone + ')';
        }
        $("#customer-display").val(displayText);

        // 关闭模态框
        $("#customerModal").modal('hide');
    }

    $("#form-salesOrder-edit").validate({
        focusCleanup: true,
        rules: {
            customerName: {
                required: true
            }
        },
        ignore: "input[type='hidden'], input[type='file'], .select2-input"
    });

    // 添加新商品明细
    function addNewDetail() {
        var detailHtml = '<tr id="new-row-' + rowIndex + '">' +
            '<td>' +
            '<input type="checkbox" name="combo-item-select" value="' + rowIndex + '">' +
            '</td>' +
            '<td>' +
            '<select class="form-control product-select" id="product-' + rowIndex + '" data-row="' + rowIndex + '">' +
            '<option value="">请选择商品</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<select class="form-control sku-select" id="sku-' + rowIndex + '" name="newSkuIds[]" data-row="' + rowIndex + '">' +
            '<option value="">请选择SKU</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control quantity" id="quantity-' + rowIndex + '" name="newQuantities[]" value="1" style="width:100%" onchange="checkNewQuantity(' + rowIndex + ')">' +
            '</td>' +
            '<td>' +
            '<span class="available-stock" id="available-stock-' + rowIndex + '">0</span>' +
            '</td>' +
            '<td>' +
            '<span id="unit-price-display-' + rowIndex + '">0.00</span>' +
            '<input type="hidden" class="unit-price" id="unit-price-' + rowIndex + '" name="newUnitPrices[]" value="0">' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control discount new-discount" id="discount-' + rowIndex + '" name="newDiscounts[]" value="0" style="width:100%" onchange="calculateRowAmount(' + rowIndex + ')">' +
            '</td>' +
            '<td>' +
            '<span class="row-amount" id="amount-' + rowIndex + '">0.00</span>' +
            '</td>' +
            '<td>' +
            '<span class="badge badge-primary">新增</span>' +
            '</td>' +
            '<td>' +
            '<a class="btn btn-primary btn-sm" onclick="insertDetailAfter(' + rowIndex + ')" style="margin-right: 5px;"><i class="fa fa-plus"></i> 添加</a>' +
            '<a class="btn btn-danger" onclick="removeNewDetail(' + rowIndex + ')" style="font-size: 10px; padding: 2px 6px;"><i class="fa fa-remove"></i></a>' +
            '</td>' +
            '</tr>';

        $("#new-details").append(detailHtml);

        // 初始化商品选择器
        initProductSelect(rowIndex);

        var currentRowIndex = rowIndex;
        rowIndex++;

        return currentRowIndex;
    }

    // 初始化商品选择器
    function initProductSelect(rowIndex) {
        $("#product-" + rowIndex).select2({
            placeholder: "请选择商品",
            allowClear: true,
            ajax: {
                url: prefix + "/searchProducts",
                dataType: 'json',
                delay: 250,
                method: 'POST',
                data: function (params) {
                    return {
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        options.push({
                            id: item.productId,
                            text: item.productName
                        });
                    });
                    return {
                        results: options
                    };
                },
                cache: true
            }
        }).on("change", function() {
            // 使用data属性获取行索引
            const rowIdx = $(this).data("row");
            loadSkus(rowIdx);
        });
    }

    // 初始化已有明细的商品选择器
    function initExistingProductSelect(rowIndex) {
        $("#existing-product-" + rowIndex).select2({
            placeholder: "请选择商品",
            allowClear: true,
            ajax: {
                url: prefix + "/searchProducts",
                dataType: 'json',
                delay: 250,
                method: 'POST',
                data: function (params) {
                    return {
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        options.push({
                            id: item.productId,
                            text: item.productName
                        });
                    });
                    return {
                        results: options
                    };
                },
                cache: true
            }
        }).on("change", function() {
            // 使用data属性获取行索引
            const rowIdx = $(this).data("row");
            loadExistingSkus(rowIdx);
        });
    }

    // 初始化已有明细的SKU选择器
    function initExistingSkuSelect(rowIndex) {
        var productId = $("#existing-row-" + rowIndex + " .existing-product-id").val();

        $("#existing-sku-" + rowIndex).select2({
            placeholder: "请选择SKU",
            allowClear: true,
            ajax: {
                url: prefix + "/searchSkus",
                dataType: 'json',
                delay: 250,
                method: 'POST',
                data: function (params) {
                    return {
                        productId: productId,
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        options.push({
                            id: item.skuId,
                            text: item.color + ' ' + item.size,
                            salePrice: item.salePrice || 0,
                            stock: item.availableStock
                        });
                    });
                    return {
                        results: options
                    };
                },
                cache: true
            }
        }).on('change', function() {
            // 使用data属性获取行索引
            const rowIdx = $(this).data("row");
            loadExistingSkuInfo(rowIdx);
        });
    }

    // 加载SKU列表
    function loadSkus(rowIndex) {
        var productId = $("#product-" + rowIndex).val();
        if (!productId) {
            return;
        }

        console.log("加载SKU，行索引: " + rowIndex + ", 商品ID: " + productId);

        // 获取当前行已选择的SKU ID并从已添加列表中移除
        var currentSkuId = $("#sku-" + rowIndex).val();
        if (currentSkuId) {
            var currentSkuIdInt = parseInt(currentSkuId);
            console.log("准备移除SKU ID:", currentSkuIdInt, "当前addedSkuIds:", JSON.stringify(addedSkuIds));

            var index = addedSkuIds.indexOf(currentSkuIdInt);
            if (index > -1) {
                addedSkuIds.splice(index, 1);
                console.log("从已添加SKU列表中移除:", currentSkuIdInt, "移除后addedSkuIds:", JSON.stringify(addedSkuIds));
            }
            // 移除库存信息
            delete stockInfo[currentSkuId];
        }

        // 重新初始化SKU选择框
        if ($("#sku-" + rowIndex).hasClass("select2-hidden-accessible")) {
            $("#sku-" + rowIndex).select2('destroy');
        }

        // 重置SKU选择框
        $("#sku-" + rowIndex).empty().append('<option value="">请选择SKU</option>');

        // 重置数量为默认的1
        $("#quantity-" + rowIndex).val(1);

        // 重置相关数据
        $("#unit-price-" + rowIndex).val(0);
        $("#unit-price-display-" + rowIndex).text("0.00");
        $("#available-stock-" + rowIndex).text("0");
        calculateRowAmount(rowIndex);

        // 初始化SKU Select2
        $("#sku-" + rowIndex).select2({
            placeholder: "请选择SKU",
            allowClear: true,
            ajax: {
                url: prefix + "/searchSkus",
                dataType: 'json',
                delay: 250,
                method:'POST',
                data: function (params) {
                    return {
                        productId: productId,
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        // 检查是否已在其他行中选择了这个SKU
                        var duplicateFound = false;

                        // 遍历所有行查找重复
                        $("#detail-table tbody tr").each(function() {
                            var rowId = $(this).attr("id");
                            if (rowId) {
                                var rowIdx = rowId.replace("row-", "").replace("existing-row-", "");
                                // 跳过当前行
                                if (rowIdx != rowIndex) {
                                    var otherSkuId = $("#sku-" + rowIdx).val();
                                    if (otherSkuId && parseInt(otherSkuId) === parseInt(item.skuId)) {
                                        duplicateFound = true;
                                        return false; // 跳出循环
                                    }
                                }
                            }
                        });

                        if (!duplicateFound) {
                            options.push({
                                id: item.skuId,
                                text: item.color + ' ' + item.size ,
                                salePrice: item.salePrice || 0,
                                stock: item.availableStock
                            });
                        }
                    });
                    return { results: options };
                },
                cache: true
            }
        }).on('change', function() {
            // 使用data属性获取行索引
            const rowIdx = $(this).data("row");
            loadSkuInfo(rowIdx);
        });
    }

    // 加载SKU信息
    function loadSkuInfo(rowIndex) {
        var skuSelect = $("#sku-" + rowIndex);
        var selectedSkuId = skuSelect.val();

        if (!selectedSkuId) {
            return; // 如果没有选择SKU，直接返回
        }

        // 将selectedSkuId转换为整数以确保一致比较
        var selectedSkuIdInt = parseInt(selectedSkuId);
        var selectedOption = skuSelect.select2('data')[0];

        // 用于调试 - 打印当前的addedSkuIds
        console.log("当前添加的SKU IDs:", JSON.stringify(addedSkuIds));
        console.log("正在添加SKU ID:", selectedSkuIdInt);

        if (selectedOption) {
            // 检查是否已经存在相同的SKU（检查完全匹配和确保不是同一行）
            var duplicateFound = false;

            // 遍历所有行查找重复
            $("#detail-table tbody tr").each(function() {
                var rowId = $(this).attr("id");
                if (rowId) {
                    var rowIdx = rowId.replace("row-", "").replace("existing-row-", "");
                    // 跳过当前行
                    if (rowIdx != rowIndex) {
                        var otherSkuId = $("#sku-" + rowIdx).val();
                        if (otherSkuId && parseInt(otherSkuId) === selectedSkuIdInt) {
                            duplicateFound = true;
                            return false; // 跳出循环
                        }
                    }
                }
            });

            if (duplicateFound) {
                $.modal.alertWarning("该SKU已经添加，不能重复添加");
                skuSelect.val("").trigger('change');
                return;
            }

            // 添加到已选SKU列表
            addedSkuIds.push(selectedSkuIdInt);
            console.log("添加后的SKU IDs:", JSON.stringify(addedSkuIds));

            // 存储SKU库存信息
            var availableStock = selectedOption.stock || 0;
            stockInfo[selectedSkuId] = availableStock;

            // 显示可用库存
            $("#available-stock-" + rowIndex).text(availableStock);

            // 设置单价
            $("#unit-price-" + rowIndex).val(selectedOption.salePrice || 0);
            $("#unit-price-display-" + rowIndex).text((selectedOption.salePrice || 0).toFixed(2));

            // 确保数量不为负数
            var quantity = parseInt($("#quantity-" + rowIndex).val()) || 1;
            if (quantity < 1) {
                $("#quantity-" + rowIndex).val(1);
            }

            // 计算金额
            calculateRowAmount(rowIndex);
        }
    }

    // 加载已有明细的SKU列表
    function loadExistingSkus(rowIndex) {
        var productId = $("#existing-product-" + rowIndex).val();
        if (!productId) {
            return;
        }

        console.log("加载已有明细SKU，行索引: " + rowIndex + ", 商品ID: " + productId);

        // 获取当前行已选择的SKU ID并从已添加列表中移除
        var currentSkuId = $("#existing-sku-" + rowIndex).val();
        if (currentSkuId) {
            var currentSkuIdInt = parseInt(currentSkuId);
            console.log("准备移除SKU ID:", currentSkuIdInt, "当前addedSkuIds:", JSON.stringify(addedSkuIds));

            var index = addedSkuIds.indexOf(currentSkuIdInt);
            if (index > -1) {
                addedSkuIds.splice(index, 1);
                console.log("从已添加SKU列表中移除:", currentSkuIdInt, "移除后addedSkuIds:", JSON.stringify(addedSkuIds));
            }
            // 移除库存信息
            delete stockInfo[currentSkuId];
        }

        // 重新初始化SKU选择框
        if ($("#existing-sku-" + rowIndex).hasClass("select2-hidden-accessible")) {
            $("#existing-sku-" + rowIndex).select2('destroy');
        }

        // 重置SKU选择框
        $("#existing-sku-" + rowIndex).empty().append('<option value="">请选择SKU</option>');

        // 重置相关数据
        $("#existing-stock-" + $("#existing-row-" + rowIndex + " .existing-sku-id").val()).text("加载中...");
        calculateExistingRowAmount($("#existing-row-" + rowIndex + " .existing-quantity")[0]);

        // 初始化SKU Select2
        $("#existing-sku-" + rowIndex).select2({
            placeholder: "请选择SKU",
            allowClear: true,
            ajax: {
                url: prefix + "/searchSkus",
                dataType: 'json',
                delay: 250,
                method:'POST',
                data: function (params) {
                    return {
                        productId: productId,
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        // 检查是否已在其他行中选择了这个SKU
                        var duplicateFound = false;

                        // 遍历所有行查找重复
                        $("#detail-table tbody tr").each(function() {
                            var rowId = $(this).attr("id");
                            if (rowId) {
                                var rowIdx = rowId.replace("existing-row-", "").replace("new-row-", "");
                                // 跳过当前行
                                if (rowIdx != rowIndex) {
                                    var otherSkuId = $("#existing-sku-" + rowIdx).val() || $("#sku-" + rowIdx).val();
                                    if (otherSkuId && parseInt(otherSkuId) === parseInt(item.skuId)) {
                                        duplicateFound = true;
                                        return false; // 跳出循环
                                    }
                                }
                            }
                        });

                        if (!duplicateFound) {
                            options.push({
                                id: item.skuId,
                                text: item.color + ' ' + item.size ,
                                salePrice: item.salePrice || 0,
                                stock: item.availableStock
                            });
                        }
                    });
                    return { results: options };
                },
                cache: true
            }
        }).on('change', function() {
            // 使用data属性获取行索引
            const rowIdx = $(this).data("row");
            loadExistingSkuInfo(rowIdx);
        });
    }

    // 加载已有明细的SKU信息
    function loadExistingSkuInfo(rowIndex) {
        var skuSelect = $("#existing-sku-" + rowIndex);
        var selectedSkuId = skuSelect.val();
        var detailId = skuSelect.data("detail-id");

        if (!selectedSkuId) {
            return; // 如果没有选择SKU，直接返回
        }

        // 将selectedSkuId转换为整数以确保一致比较
        var selectedSkuIdInt = parseInt(selectedSkuId);
        var selectedOption = skuSelect.select2('data')[0];

        // 用于调试 - 打印当前的addedSkuIds
        console.log("当前添加的SKU IDs:", JSON.stringify(addedSkuIds));
        console.log("正在更新已有明细SKU ID:", selectedSkuIdInt);

        if (selectedOption) {
            // 检查是否已经存在相同的SKU（检查完全匹配和确保不是同一行）
            var duplicateFound = false;

            // 遍历所有行查找重复
            $("#detail-table tbody tr").each(function() {
                var rowId = $(this).attr("id");
                if (rowId) {
                    var rowIdx = rowId.replace("existing-row-", "").replace("new-row-", "");
                    // 跳过当前行
                    if (rowIdx != rowIndex) {
                        var otherSkuId = $("#existing-sku-" + rowIdx).val() || $("#sku-" + rowIdx).val();
                        if (otherSkuId && parseInt(otherSkuId) === selectedSkuIdInt) {
                            duplicateFound = true;
                            return false; // 跳出循环
                        }
                    }
                }
            });

            if (duplicateFound) {
                $.modal.alertWarning("该SKU已经添加，不能重复添加");
                skuSelect.val("").trigger('change');
                return;
            }

            // 更新已有SKU的数量记录
            var quantity = parseInt($("#existing-row-" + rowIndex + " .existing-quantity").val()) || 1;
            existingSkuQuantity[selectedSkuId] = quantity;

            // 更新detailId和skuId的映射
            detailToSkuMap[detailId] = selectedSkuId;

            // 存储SKU库存信息
            var availableStock = selectedOption.stock || 0;
            systemStockInfo[selectedSkuId] = availableStock;

            // 计算实际可用库存 = 系统可用库存 + 当前订单锁定的库存
            var actualAvailable = availableStock + quantity;

            // 更新库存显示
            $("#existing-stock-" + selectedSkuId).text(actualAvailable);

            // 存储库存信息
            stockInfo[selectedSkuId] = actualAvailable;

            // 更新隐藏字段
            $("#existing-row-" + rowIndex + " .existing-sku-id").val(selectedSkuId);
            $("#existing-row-" + rowIndex + " .existing-unit-price").val(selectedOption.salePrice || 0);

            // 添加隐藏字段来传递更新后的SKU ID
            var existingSkuInput = $("input[name='existingSkuIds[" + detailId + "]']");
            if (existingSkuInput.length === 0) {
                $("#deleted-details-container").append('<input type="hidden" name="existingSkuIds[' + detailId + ']" value="' + selectedSkuId + '">');
            } else {
                existingSkuInput.val(selectedSkuId);
            }

            // 更新单价显示
            $("#existing-row-" + rowIndex).find("td:eq(4)").text((selectedOption.salePrice || 0).toFixed(2));

            // 计算金额
            calculateExistingRowAmount($("#existing-row-" + rowIndex + " .existing-quantity")[0]);
        }
    }

    // 检查新添加SKU的数量
    function checkNewQuantity(rowIndex) {
        var quantity = parseInt($("#quantity-" + rowIndex).val()) || 0;

        // 数量不能小于1
        if (quantity < 1) {
            $("#quantity-" + rowIndex).val(1);
            quantity = 1;
        }

        // 重新计算金额
        calculateRowAmount(rowIndex);
    }

    // 计算已有明细的行金额
    function calculateExistingRowAmount(input) {
        var row = $(input).closest("tr");
        var detailId = row.find(".existing-detail-id").val();
        var skuId = detailToSkuMap[detailId];

        if (!skuId) return;

        if ($(input).hasClass('existing-quantity')) {
            // 数量输入框变更
            var quantity = parseInt($(input).val()) || 0;

            // 数量不能小于1
            if (quantity < 1) {
                $(input).val(1);
                quantity = 1;
            }

            // 更新已有SKU的数量记录
            existingSkuQuantity[skuId] = quantity;
        }

        // 计算金额
        var quantity = parseInt(row.find(".existing-quantity").val()) || 0;
        var unitPrice = parseFloat(row.find(".existing-unit-price").val()) || 0;
        var discount = parseFloat(row.find(".existing-discount").val()) || 0;

        var amount = quantity * unitPrice - discount;
        amount = amount < 0 ? 0 : amount;

        // 更新行金额显示
        $("#existing-amount-" + detailId).text(amount.toFixed(2));

        // 重新计算总金额
        calculateTotal();
    }

    // 计算单行金额
    function calculateRowAmount(rowIndex) {
        var quantity = parseFloat($("#quantity-" + rowIndex).val()) || 0;
        var unitPrice = parseFloat($("#unit-price-" + rowIndex).val()) || 0;
        var discount = parseFloat($("#discount-" + rowIndex).val()) || 0;

        var amount = quantity * unitPrice - discount;
        amount = amount < 0 ? 0 : amount;

        $("#amount-" + rowIndex).text(amount.toFixed(2));

        // 计算总金额
        calculateTotal();
    }

    // 计算总金额（考虑已有的和新增的明细）
    function calculateTotal() {
        var totalAmount = 0;
        var totalDiscount = 0;

        // 已有明细的金额计算
        $("#existing-details tr:visible").each(function() {
            var unitPrice = parseFloat($(this).find(".existing-unit-price").val()) || 0;
            var quantity = parseFloat($(this).find(".existing-quantity").val()) || 0;
            var discount = parseFloat($(this).find(".existing-discount").val()) || 0;

            totalAmount += unitPrice * quantity;
            totalDiscount += discount;
        });

        // 新增明细的金额计算（包括在new-details中的和插入在existing-details中的）
        $("#new-details tr:visible, #existing-details tr.inserted-after-existing:visible").each(function() {
            var unitPrice = parseFloat($(this).find(".unit-price").val()) || 0;
            var quantity = parseFloat($(this).find(".quantity").val()) || 0;
            var discount = parseFloat($(this).find(".new-discount").val()) || 0;

            totalAmount += unitPrice * quantity;
            totalDiscount += discount;
        });

        var actualAmount = totalAmount - totalDiscount;
        actualAmount = actualAmount < 0 ? 0 : actualAmount;

        $("#total-amount").text(totalAmount.toFixed(2));
        $("#total-discount").text(totalDiscount.toFixed(2));
        $("#actual-amount").text(actualAmount.toFixed(2));
    }

    // 移除新增的明细行
    function removeNewDetail(rowIndex) {
        var skuId = $("#sku-" + rowIndex).val();
        // 从已添加SKU列表中移除
        if (skuId) {
            var index = addedSkuIds.indexOf(parseInt(skuId));
            if (index > -1) {
                addedSkuIds.splice(index, 1);
            }

            // 移除库存信息
            delete stockInfo[skuId];
        }

        $("#new-row-" + rowIndex).remove();
        calculateTotal();
    }

    // 移除已有的明细行（仅对未出库的商品生效）
    function removeExistingDetail(index, detailId, skuId) {
        // 从已添加SKU列表中移除
        var skuIndex = addedSkuIds.indexOf(parseInt(skuId));
        if (skuIndex > -1) {
            addedSkuIds.splice(skuIndex, 1);
        }

        // 从已有SKU的数量记录中移除
        delete existingSkuQuantity[skuId];

        // 从库存信息中移除
        delete stockInfo[skuId];
        delete systemStockInfo[skuId];

        // 从detailId和skuId的映射中移除
        delete detailToSkuMap[detailId];

        // 添加到删除列表中
        deletedDetailIds.push(detailId);

        // 创建隐藏的输入字段来传递被删除的detailId
        $("#deleted-details-container").append('<input type="hidden" name="deletedDetailIds[]" value="' + detailId + '">');

        // 隐藏该行
        $("#existing-row-" + index).hide();
        calculateTotal();
    }

    // 验证数据有效性
    function validateDetails() {
        // 检查客户ID
        var customerIdValue = $("#customerId").val();

        console.log('表单验证时的客户ID:', customerIdValue);
        console.log('customerId元素:', $("#customerId")[0]);
        console.log('所有隐藏字段:', $("input[type=hidden]").map(function() { return this.name + '=' + this.value; }).get());

        // 验证客户是否已选择
        if (!customerIdValue) {
            $.modal.alertWarning("请选择客户");
            return false;
        }

        var valid = true;

        // 检查已有明细的可编辑字段（排除插入的新行）
        $("#existing-details tr:visible").each(function() {
            // 跳过插入的新行
            if ($(this).hasClass("inserted-after-existing")) {
                return true; // 继续下一个循环
            }

            var detailId = $(this).find(".existing-detail-id").val();
            var quantity = parseInt($(this).find(".existing-quantity").val()) || 0;
            var discount = parseFloat($(this).find(".existing-discount").val()) || 0;

            if (quantity <= 0) {
                valid = false;
                $.modal.alertWarning("数量必须大于0");
                return false;
            }

            if (discount < 0) {
                valid = false;
                $.modal.alertWarning("优惠金额不能为负数");
                return false;
            }
        });

        // 检查新增明细（包括在new-details中的和插入在existing-details中的）
        $("#new-details tr:visible, #existing-details tr.inserted-after-existing:visible").each(function() {
            var skuId = $(this).find(".sku-select").val();
            var quantity = parseInt($(this).find(".quantity").val()) || 0;
            var discount = parseFloat($(this).find(".discount").val()) || 0;

            if (!skuId) {
                valid = false;
                $.modal.alertWarning("请选择SKU");
                return false;
            }

            if (quantity <= 0) {
                valid = false;
                $.modal.alertWarning("数量必须大于0");
                return false;
            }

            if (discount < 0) {
                valid = false;
                $.modal.alertWarning("优惠金额不能为负数");
                return false;
            }
        });

        return valid;
    }

    function submitHandler() {
        // 先手动检查自定义验证
        if (!validateDetails()) {
            return false;
        }

        // 确保所有数值输入都有效
        $(".quantity, .existing-quantity").each(function() {
            if ($(this).val() === "" || isNaN(parseFloat($(this).val()))) {
                $(this).val(1);
            }
        });

        $(".discount, .existing-discount, .new-discount").each(function() {
            if ($(this).val() === "" || isNaN(parseFloat($(this).val()))) {
                $(this).val(0);
            }
        });

        // 然后验证表单其他字段
        if ($.validate.form()) {
            // 直接使用 jQuery 的 ajax 提交
            $.ajax({
                url: prefix + "/edit",
                type: "POST",
                data: $("#form-salesOrder-edit").serialize(),
                success: function(result) {
                    if (result.code == 0) {
                        $.modal.alertSuccess("操作成功");
                        $.modal.close();
                        $.table.refresh();
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }
            });
        }
    }

    // 打印销售订单（含价格）
    function printOrderWithPrice() {
        var orderId = $("input[name='orderId']").val();
        var url = prefix + "/orderPrint/" + orderId;
        window.open(url, "_blank", "height=600,width=800,top=100,left=200");
    }

    // 打印出库单（不含价格）
    function printOutbound() {
        var orderId = $("input[name='orderId']").val();
        var url = prefix + "/outboundPrint/" + orderId;
        window.open(url, "_blank", "height=600,width=800,top=100,left=200");
    }

    // ==================== 组合相关功能 ====================

    // 初始化商品组合下拉框
    function initComboSelect() {
        $("#combo-select").select2({
            placeholder: "请选择商品组合",
            allowClear: true,
            ajax: {
                url: prefix + "/searchProductCombos",
                dataType: 'json',
                delay: 250,
                method: 'POST',
                data: function (params) {
                    return {
                        keyword: params.term // 搜索关键字
                    };
                },
                processResults: function (data) {
                    var options = [];
                    $.each(data.data, function (index, item) {
                        options.push({
                            id: item.comboId,
                            text: item.comboName
                        });
                    });
                    return {
                        results: options
                    };
                },
                cache: true
            }
        });
    }

    // 全选/取消全选复选框
    function toggleAllCheckboxes(source) {
        var checkboxes = document.querySelectorAll('input[name="combo-item-select"]');
        for(var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = source.checked;
        }
    }

    // 一键添加组合商品
    function addComboItems() {
        var comboId = $("#combo-select").val();
        if (!comboId) {
            $.modal.alertWarning("请先选择一个商品组合");
            return;
        }

        console.log("正在添加组合ID:", comboId);

        // 获取组合项目详情
        $.ajax({
            url: prefix + "/getProductComboItems/" + comboId,
            type: "get",
            success: function(result) {
                if (result.code == 0 && result.data && result.data.length > 0) {
                    // 保存原始添加行的信息
                    var rows = result.data;
                    console.log("组合项目数据:", rows);

                    // 批量添加商品/SKU
                    $.each(rows, function(index, item) {
                        if (item.item_type == 1) {
                            // 商品类型，添加商品行
                            addProductFromCombo(item);
                        } else if (item.item_type == 2) {
                            // SKU类型，添加SKU行
                            addSkuFromCombo(item);
                        }
                    });

                    // 清空组合选择框
                    $("#combo-select").val(null).trigger('change');
                    $.modal.msgSuccess("已成功添加组合商品");
                } else {
                    $.modal.msgWarning("此商品组合不包含任何商品");
                }
            },
            error: function() {
                $.modal.msgError("获取组合项目失败");
            }
        });
    }

    // 从组合中添加商品
    function addProductFromCombo(item) {
        // 构造一个新行
        var newRowIndex = addNewDetail();
        console.log("添加商品，当前行索引:", newRowIndex, "商品ID:", item.ref_id);

        // 等待DOM更新后设置商品
        setTimeout(function() {
            // 查询商品信息
            $.ajax({
                url: prefix + "/searchProducts",
                type: "post",
                data: {
                    keyword: ""
                },
                success: function(productResult) {
                    if (productResult.code == 0 && productResult.data) {
                        var products = productResult.data;
                        var targetProduct = null;

                        // 查找组合中指定的商品
                        for(var i = 0; i < products.length; i++) {
                            if(products[i].productId == item.ref_id) {
                                targetProduct = products[i];
                                break;
                            }
                        }

                        if(targetProduct) {
                            console.log("找到匹配商品:", targetProduct);

                            // 设置商品选择器的值
                            var productSelect = $("#product-" + newRowIndex);
                            var newOption = new Option(targetProduct.productName, targetProduct.productId, true, true);
                            productSelect.append(newOption).trigger('change');

                            // 手动触发商品选择事件来加载SKU
                            loadSkus(newRowIndex);
                        } else {
                            console.log("未找到匹配的商品ID:", item.ref_id);
                        }
                    }
                },
                error: function() {
                    console.log("查询商品信息失败");
                }
            });
        }, 100);
    }

    // 修复后的从组合中添加SKU函数
    function addSkuFromCombo(item) {
        console.log("添加SKU，SKU ID:", item.ref_id);

        // 检查是否已添加
        if(addedSkuIds.indexOf(parseInt(item.ref_id)) > -1) {
            console.log("SKU已存在，跳过:", item.ref_id);
            return;
        }

        // 直接通过SKU ID获取SKU信息
        $.ajax({
            url: prefix + "/getSkuInfo/" + item.ref_id,
            type: "get",
            success: function(skuResult) {
                if (skuResult.code == 0 && skuResult.data) {
                    var targetSku = skuResult.data;
                        console.log("找到匹配SKU:", targetSku);

                        // 检查是否已添加
                        if(addedSkuIds.indexOf(parseInt(targetSku.skuId)) > -1) {
                            console.log("SKU已存在，跳过:", targetSku.skuId);
                            return;
                        }

                        // 获取对应的商品ID
                        var productId = targetSku.productId;

                        // 添加一个标准行
                        var currentIndex = addNewDetail();
                        console.log("为SKU创建新行，行索引:", currentIndex);

                        // 等待DOM更新后设置商品和SKU
                        setTimeout(function() {
                            // 查询商品信息
                            $.ajax({
                                url: prefix + "/searchProducts",
                                type: "post",
                                data: {
                                    keyword: ""
                                },
                                success: function(productResult) {
                                    if (productResult.code == 0 && productResult.data) {
                                        var products = productResult.data;
                                        var targetProduct = null;

                                        // 查找对应的商品
                                        for(var j = 0; j < products.length; j++) {
                                            if(products[j].productId == productId) {
                                                targetProduct = products[j];
                                                break;
                                            }
                                        }

                                        if(targetProduct) {
                                            console.log("找到对应商品:", targetProduct);

                                            // 设置商品选择器的值
                                            var productSelect = $("#product-" + currentIndex);
                                            var productOption = new Option(targetProduct.productName, targetProduct.productId, true, true);
                                            productSelect.append(productOption).trigger('change');

                                            // 等待商品设置完成后设置SKU
                                            setTimeout(function() {
                                                var skuSelect = $("#sku-" + currentIndex);
                                                var skuOption = new Option(targetSku.color + ' ' + targetSku.size, targetSku.skuId, true, true);
                                                skuOption.salePrice = targetSku.salePrice;
                                                skuOption.stock = targetSku.availableStock;
                                                skuSelect.append(skuOption).trigger('change');

                                                // 手动触发SKU选择事件
                                                loadSkuInfo(currentIndex);
                                            }, 100);
                                        } else {
                                            console.log("未找到对应的商品ID:", productId);
                                        }
                                    }
                                },
                                error: function() {
                                    console.log("查询商品信息失败");
                                }
                            });
                        }, 200);
                    } else {
                        console.log("未找到匹配的SKU ID:", item.ref_id);
                    }
                }
            },
            error: function() {
                console.log("查询SKU信息失败");
            }
        });
    }

    // 保存为商品组合
    function saveAsProductCombo() {
        // 获取选中的复选框
        var checkedItems = $('input[name="combo-item-select"]:checked');

        if (checkedItems.length === 0) {
            $.modal.alertWarning("请至少选择一个商品或SKU后再保存为组合");
            return;
        }

        // 收集选中商品的信息
        var selectedItems = [];
        var summaryHtml = '';

        console.log("选中的商品数量:", checkedItems.length);

        checkedItems.each(function() {
            var rowValue = $(this).val();
            var row;
            var productId, skuId, productName;

            if (rowValue.startsWith('existing-')) {
                // 已有明细
                var rowIndex = rowValue.replace('existing-', '');
                row = $("#existing-row-" + rowIndex);
                productId = row.find(".existing-product-id").val();
                skuId = row.find(".existing-sku-id").val();
                productName = row.find(".existing-product-name").val();
            } else {
                // 新添加的明细
                var rowIndex = rowValue;
                row = $("#row-" + rowIndex);
                productId = row.find(".product-select").val();
                skuId = row.find(".sku-select").val();
                try {
                    productName = row.find(".product-select").select2('data')[0].text || '';
                } catch (e) {
                    console.error("获取商品名称失败:", e);
                }
            }

            console.log("处理行:", rowValue, "商品ID:", productId, "SKU ID:", skuId);

            if (productId && skuId) {
                // SKU类型
                selectedItems.push({
                    itemType: 2, // SKU类型
                    refId: skuId
                });
                summaryHtml += '<p>SKU: ' + (productName || '未知商品') + '</p>';
            } else if (productId) {
                // 商品类型
                selectedItems.push({
                    itemType: 1, // 商品类型
                    refId: productId
                });
                summaryHtml += '<p>商品: ' + (productName || '未知商品') + '</p>';
            }
        });

        console.log("选中的商品项:", selectedItems);

        if (selectedItems.length === 0) {
            $.modal.alertWarning("所选项目中没有有效的商品或SKU，请重新选择");
            return;
        }

        // 更新摘要信息
        if (summaryHtml !== '') {
            $("#selected-items-summary").html(summaryHtml);
        } else {
            $("#selected-items-summary").html('<p class="text-muted">未选择任何商品</p>');
        }

        // 打开保存为组合对话框
        $("#saveComboModal").modal("show");
    }

    // 提交保存商品组合
    function submitSaveCombo() {
        var comboName = $("#comboName").val();
        if (!comboName) {
            $.modal.alertWarning("请输入组合名称");
            return;
        }

        // 重新收集选中的商品信息
        var checkedItems = $('input[name="combo-item-select"]:checked');
        var items = [];

        checkedItems.each(function() {
            var rowValue = $(this).val();
            var row;
            var productId, skuId;

            if (rowValue.startsWith('existing-')) {
                // 已有明细
                var rowIndex = rowValue.replace('existing-', '');
                row = $("#existing-row-" + rowIndex);
                productId = row.find(".existing-product-id").val();
                skuId = row.find(".existing-sku-id").val();
            } else {
                // 新添加的明细
                var rowIndex = rowValue;
                row = $("#row-" + rowIndex);
                productId = row.find(".product-select").val();
                skuId = row.find(".sku-select").val();
            }

            if (productId && skuId) {
                // SKU类型
                items.push({
                    itemType: 2,
                    refId: skuId
                });
            } else if (productId) {
                // 商品类型
                items.push({
                    itemType: 1,
                    refId: productId
                });
            }
        });

        // 记录商品项数量
        console.log("提交的商品项数量:", items.length);

        // 构造保存组合的数据
        var comboData = {
            comboName: comboName,
            status: $("input[name='status']:checked").val(),
            remark: $("#form-save-combo textarea[name='remark']").val(),
            items: JSON.stringify(items)
        };

        // 发送请求保存商品组合
        $.ajax({
            url: comboPrefix + "/saveProductComboWithItems",
            type: "post",
            data: comboData,
            success: function(result) {
                if (result.code == 0) {
                    $.modal.msgSuccess("商品组合保存成功");
                    $("#saveComboModal").modal("hide");
                    // 重置表单
                    $("#form-save-combo")[0].reset();
                    $("#selected-items-summary").html('<p class="text-muted">未选择任何商品</p>');
                } else {
                    $.modal.alertError(result.msg || "保存失败");
                }
            },
            error: function() {
                $.modal.alertError("保存失败，请稍后重试");
            }
        });
    }

    // 在指定行后面插入新的商品行
    function insertDetailAfter(afterRowIndex) {
        var detailHtml = '<tr id="new-row-' + rowIndex + '">' +
            '<td>' +
            '<input type="checkbox" name="combo-item-select" value="' + rowIndex + '">' +
            '</td>' +
            '<td>' +
            '<select class="form-control product-select" id="product-' + rowIndex + '" data-row="' + rowIndex + '">' +
            '<option value="">请选择商品</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<select class="form-control sku-select" id="sku-' + rowIndex + '" name="newSkuIds[]" data-row="' + rowIndex + '">' +
            '<option value="">请选择SKU</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control quantity" id="quantity-' + rowIndex + '" name="newQuantities[]" value="1" style="width:100%" onchange="checkNewQuantity(' + rowIndex + ')">' +
            '</td>' +
            '<td>' +
            '<span class="available-stock" id="available-stock-' + rowIndex + '">0</span>' +
            '</td>' +
            '<td>' +
            '<span id="unit-price-display-' + rowIndex + '">0.00</span>' +
            '<input type="hidden" class="unit-price" id="unit-price-' + rowIndex + '" name="newUnitPrices[]" value="0">' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control discount new-discount" id="discount-' + rowIndex + '" name="newDiscounts[]" value="0" style="width:100%" onchange="calculateRowAmount(' + rowIndex + ')">' +
            '</td>' +
            '<td>' +
            '<span class="row-amount" id="amount-' + rowIndex + '">0.00</span>' +
            '</td>' +
            '<td>' +
            '<span class="badge badge-primary">新增</span>' +
            '</td>' +
            '<td>' +
            '<a class="btn btn-primary btn-sm" onclick="insertDetailAfter(' + rowIndex + ')" style="margin-right: 5px;"><i class="fa fa-plus"></i> 添加</a>' +
            '<a class="btn btn-danger" onclick="removeNewDetail(' + rowIndex + ')" style="font-size: 10px; padding: 2px 6px;"><i class="fa fa-remove"></i></a>' +
            '</td>' +
            '</tr>';

        // 在指定行后面插入新行
        $("#new-row-" + afterRowIndex).after(detailHtml);

        // 保存当前行索引
        var currentIndex = rowIndex;
        rowIndex++;

        // 初始化商品选择器
        initProductSelect(currentIndex);

        return currentIndex;
    }

    // 在已有明细行后面插入新的商品行
    function insertDetailAfterExisting(existingRowIndex) {
        var detailHtml = '<tr id="new-row-' + rowIndex + '" class="inserted-after-existing">' +
            '<td>' +
            '<input type="checkbox" name="combo-item-select" value="' + rowIndex + '">' +
            '</td>' +
            '<td>' +
            '<select class="form-control product-select" id="product-' + rowIndex + '" data-row="' + rowIndex + '">' +
            '<option value="">请选择商品</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<select class="form-control sku-select" id="sku-' + rowIndex + '" name="newSkuIds[]" data-row="' + rowIndex + '">' +
            '<option value="">请选择SKU</option>' +
            '</select>' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control quantity" id="quantity-' + rowIndex + '" name="newQuantities[]" value="1" style="width:100%" onchange="checkNewQuantity(' + rowIndex + ')">' +
            '</td>' +
            '<td>' +
            '<span class="available-stock" id="available-stock-' + rowIndex + '">0</span>' +
            '</td>' +
            '<td>' +
            '<span id="unit-price-display-' + rowIndex + '">0.00</span>' +
            '<input type="hidden" class="unit-price" id="unit-price-' + rowIndex + '" name="newUnitPrices[]" value="0">' +
            '</td>' +
            '<td>' +
            '<input type="number" class="form-control discount new-discount" id="discount-' + rowIndex + '" name="newDiscounts[]" value="0" style="width:100%" onchange="calculateRowAmount(' + rowIndex + ')">' +
            '</td>' +
            '<td>' +
            '<span class="row-amount" id="amount-' + rowIndex + '">0.00</span>' +
            '</td>' +
            '<td>' +
            '<span class="badge badge-success">在此插入</span>' +
            '</td>' +
            '<td style="white-space: nowrap;">' +
            '<a class="btn btn-primary btn-sm" onclick="insertDetailAfter(' + rowIndex + ')" style="margin-right: 3px;"><i class="fa fa-plus"></i> 添加</a>' +
            '<a class="btn btn-danger" onclick="removeNewDetail(' + rowIndex + ')" style="font-size: 10px; padding: 2px 6px;"><i class="fa fa-remove"></i></a>' +
            '</td>' +
            '</tr>';

        // 在已有明细行后面直接插入新行
        $("#existing-row-" + existingRowIndex).after(detailHtml);

        // 保存当前行索引
        var currentIndex = rowIndex;
        rowIndex++;

        // 初始化商品选择器
        initProductSelect(currentIndex);

        return currentIndex;
    }

</script>
</body>
</html>