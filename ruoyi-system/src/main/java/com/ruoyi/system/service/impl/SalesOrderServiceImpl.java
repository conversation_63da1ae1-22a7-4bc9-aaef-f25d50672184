package com.ruoyi.system.service.impl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.Product;
import com.ruoyi.system.domain.SalesOrderDetail;
import com.ruoyi.system.domain.Sku;
import com.ruoyi.system.service.IProductService;
import com.ruoyi.system.service.ISalesOrderDetailService;
import com.ruoyi.system.service.ISkuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SalesOrderMapper;
import com.ruoyi.system.domain.SalesOrder;
import com.ruoyi.system.service.ISalesOrderService;
import com.ruoyi.common.core.text.Convert;
import org.springframework.transaction.annotation.Transactional;

/**
 * 销售订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Service
public class SalesOrderServiceImpl implements ISalesOrderService
{
    @Autowired
    private SalesOrderMapper salesOrderMapper;

    @Autowired
    private ISalesOrderDetailService salesOrderDetailService;

    @Autowired
    private ISkuService skuService;

    @Autowired
    private IProductService productService;

    /**
     * 查询销售订单
     *
     * @param orderId 销售订单主键
     * @return 销售订单
     */
    @Override
    public SalesOrder selectSalesOrderByOrderId(Long orderId)
    {
        return salesOrderMapper.selectSalesOrderByOrderId(orderId);
    }

    /**
     * 查询销售订单列表
     *
     * @param salesOrder 销售订单
     * @return 销售订单
     */
    @Override
    public List<SalesOrder> selectSalesOrderList(SalesOrder salesOrder)
    {
        return salesOrderMapper.selectSalesOrderList(salesOrder);
    }

    /**
     * 新增销售订单
     *
     * @param salesOrder 销售订单
     * @return 结果
     */
    @Override
    public int insertSalesOrder(SalesOrder salesOrder)
    {
        salesOrder.setCreateTime(DateUtils.getNowDate());
        return salesOrderMapper.insertSalesOrder(salesOrder);
    }

    /**
     * 修改销售订单
     *
     * @param salesOrder 销售订单
     * @return 结果
     */
    @Override
    public int updateSalesOrder(SalesOrder salesOrder)
    {
        salesOrder.setUpdateTime(DateUtils.getNowDate());
        return salesOrderMapper.updateSalesOrder(salesOrder);
    }



    /**
     * 删除销售订单信息
     *
     * @param orderId 销售订单主键
     * @return 结果
     */
    @Override
    public int deleteSalesOrderByOrderId(Long orderId)
    {
        return salesOrderMapper.deleteSalesOrderByOrderId(orderId);
    }

    // 仅修改createSalesOrder方法中的部分代码 - 添加重量和体积处理

    /**
     * 创建销售订单及明细
     */
    @Override
    @Transactional
    public int createSalesOrder(SalesOrder salesOrder, Long[] skuIds, Long[] quantities,
                                BigDecimal[] unitPrices, BigDecimal[] discounts) {
        // 生成订单编号：DD+日期+序号
        String orderCode = "DD" + DateUtils.dateTimeNow() + (int)(Math.random() * 1000);
        salesOrder.setOrderCode(orderCode);
        salesOrder.setStatus(0);  // 草稿状态
        salesOrder.setCanModify(1);  // 可修改

        // 计算订单总金额
        BigDecimal orderAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal actualAmount = BigDecimal.ZERO;

        // 保存订单主信息
        int rows = insertSalesOrder(salesOrder);

        // 保存订单明细并锁定库存
        if (skuIds != null && skuIds.length > 0) {
            for (int i = 0; i < skuIds.length; i++) {
                SalesOrderDetail detail = new SalesOrderDetail();
                detail.setOrderId(salesOrder.getOrderId());
                detail.setSkuId(skuIds[i]);

                // 获取SKU信息
                Sku sku = skuService.selectSkuBySkuId(skuIds[i]);
                if (sku != null) {
                    // 设置商品信息
                    Product product = productService.selectProductByProductId(sku.getProductId());
                    if (product != null) {
                        detail.setProductName(product.getProductName());
                    }
                    detail.setSkuProperties(sku.getColor() + " " + sku.getSize());

                    // 设置数量、单价和优惠
                    detail.setOriginalQuantity(quantities[i]);
                    detail.setModifiedQuantity(quantities[i]);
                    detail.setUnitPrice(unitPrices[i]);
                    detail.setDiscountAmount(discounts[i]);
                    detail.setShipmentStatus(0);
                    detail.setShippedQuantity(0L);

                    // 设置重量和体积
                    detail.setUnitWeight(sku.getWeight());
                    detail.setUnitVolume(sku.getVolume());

                    // 计算金额
                    BigDecimal itemAmount = unitPrices[i].multiply(new BigDecimal(quantities[i]));

                    // 计算实际金额并设置到订单明细中
                    BigDecimal itemActualAmount = itemAmount.subtract(detail.getDiscountAmount());
                    detail.setTotalAmount(itemAmount);
                    detail.setActualAmount(itemActualAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : itemActualAmount);

                    orderAmount = orderAmount.add(itemAmount);
                    discountAmount = discountAmount.add(discounts[i]);

                    // 保存明细
                    salesOrderDetailService.insertSalesOrderDetail(detail);

                    // 锁定库存
                    sku.setLockedStock(sku.getLockedStock() + quantities[i]);
                    sku.setAvailableStock(sku.getAvailableStock() - quantities[i]);
                    skuService.updateSku(sku);
                }
            }
        }

        // 计算实付金额
        actualAmount = orderAmount.subtract(discountAmount);

        // 更新订单金额
        salesOrder.setOrderAmount(orderAmount);
        salesOrder.setDiscountAmount(discountAmount);
        salesOrder.setActualAmount(actualAmount);
        updateSalesOrder(salesOrder);

        return rows;
    }

    /**
     * 更新销售订单及明细
     */
    @Override
    @Transactional
    public int updateSalesOrderWithDetails(SalesOrder salesOrder, Long[] deletedDetailIds,
                                           Map<Long, Long> existingQuantities, Long[] newSkuIds,
                                           Long[] newQuantities, BigDecimal[] newUnitPrices,
                                           BigDecimal[] newDiscounts) {
        // 判断订单是否可修改
        SalesOrder originalOrder = selectSalesOrderByOrderId(salesOrder.getOrderId());
        if (originalOrder != null && originalOrder.getCanModify() != null
                && originalOrder.getCanModify() == 0) {
            throw new RuntimeException("订单已锁定，不可修改");
        }

        // 先查询原有明细列表
        SalesOrderDetail queryDetail = new SalesOrderDetail();
        queryDetail.setOrderId(salesOrder.getOrderId());
        List<SalesOrderDetail> originalDetails = salesOrderDetailService.selectSalesOrderDetailList(queryDetail);

        // 处理订单明细和库存调整
        BigDecimal orderAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;

        // 1. 处理被删除的明细
        if (deletedDetailIds != null && deletedDetailIds.length > 0) {
            for (Long detailId : deletedDetailIds) {
                SalesOrderDetail detail = salesOrderDetailService.selectSalesOrderDetailByDetailId(detailId);
                if (detail != null && detail.getShipmentStatus() == 0) { // 只处理未出库的
                    // 释放锁定的库存
                    Sku sku = skuService.selectSkuBySkuId(detail.getSkuId());
                    if (sku != null) {
                        sku.setLockedStock(sku.getLockedStock() - detail.getModifiedQuantity());
                        sku.setAvailableStock(sku.getAvailableStock() + detail.getModifiedQuantity());
                        skuService.updateSku(sku);
                    }
                    // 删除明细
                    salesOrderDetailService.deleteSalesOrderDetailByDetailId(detailId);
                }
            }
        }

        // 2. 处理已有明细的数量更新
        if (existingQuantities != null && !existingQuantities.isEmpty()) {
            for (SalesOrderDetail detail : originalDetails) {
                // 跳过已删除的明细
                if (deletedDetailIds != null && Arrays.asList(deletedDetailIds).contains(detail.getDetailId())) {
                    continue;
                }

                // 获取新数量
                Long newQuantity = existingQuantities.get(detail.getDetailId());
                if (newQuantity != null) {
                    // 原始数量
                    Long originalQuantity = detail.getModifiedQuantity();

                    if (!originalQuantity.equals(newQuantity)) {
                        // 数量有变化
                        detail.setModifiedQuantity(newQuantity);
                        salesOrderDetailService.updateSalesOrderDetail(detail);

                        // 调整库存
                        Sku sku = skuService.selectSkuBySkuId(detail.getSkuId());
                        if (sku != null) {
                            Long quantityDiff = newQuantity - originalQuantity;
                            sku.setLockedStock(sku.getLockedStock() + quantityDiff);
                            sku.setAvailableStock(sku.getAvailableStock() - quantityDiff);
                            skuService.updateSku(sku);
                        }
                    }

                    // 计算金额
                    BigDecimal itemAmount = detail.getUnitPrice().multiply(new BigDecimal(newQuantity));
                    orderAmount = orderAmount.add(itemAmount);
                    discountAmount = discountAmount.add(detail.getDiscountAmount());
                }
            }
        }

        // 3. 处理新增的明细
        if (newSkuIds != null && newSkuIds.length > 0 && newQuantities != null && newQuantities.length > 0) {
            for (int i = 0; i < newSkuIds.length; i++) {
                if (i < newQuantities.length && newSkuIds[i] != null && newQuantities[i] != null) {
                    SalesOrderDetail detail = new SalesOrderDetail();
                    detail.setOrderId(salesOrder.getOrderId());
                    detail.setSkuId(newSkuIds[i]);
                    detail.setOriginalQuantity(newQuantities[i]);
                    detail.setModifiedQuantity(newQuantities[i]);

                    // 获取SKU信息
                    Sku sku = skuService.selectSkuBySkuId(newSkuIds[i]);
                    if (sku != null) {
                        // 设置商品信息
                        Product product = productService.selectProductByProductId(sku.getProductId());
                        if (product != null) {
                            detail.setProductName(product.getProductName());
                        }
                        detail.setSkuProperties(sku.getColor() + " " + sku.getSize());

                        // 设置单价
                        BigDecimal unitPrice = (i < newUnitPrices.length) ? newUnitPrices[i] : sku.getSalePrice();
                        detail.setUnitPrice(unitPrice != null ? unitPrice : sku.getSalePrice());

                        // 设置优惠
                        BigDecimal discount = (i < newDiscounts.length) ? newDiscounts[i] : BigDecimal.ZERO;
                        detail.setDiscountAmount(discount != null ? discount : BigDecimal.ZERO);

                        // 设置出库状态
                        detail.setShipmentStatus(0);
                        detail.setShippedQuantity(0L);

                        // 添加明细
                        salesOrderDetailService.insertSalesOrderDetail(detail);

                        // 锁定库存
                        sku.setLockedStock(sku.getLockedStock() + newQuantities[i]);
                        sku.setAvailableStock(sku.getAvailableStock() - newQuantities[i]);
                        skuService.updateSku(sku);

                        // 累计金额
                        BigDecimal itemAmount = detail.getUnitPrice().multiply(new BigDecimal(newQuantities[i]));
                        orderAmount = orderAmount.add(itemAmount);
                        discountAmount = discountAmount.add(detail.getDiscountAmount());
                    }
                }
            }
        }

        // 计算实付金额
        BigDecimal actualAmount = orderAmount.subtract(discountAmount);

        // 更新订单金额
        salesOrder.setOrderAmount(orderAmount);
        salesOrder.setDiscountAmount(discountAmount);
        salesOrder.setActualAmount(actualAmount);

        return updateSalesOrder(salesOrder);
    }

    /**
     * 批量删除销售订单
     */
    @Override
    @Transactional
    public int deleteSalesOrderByOrderIds(String orderIds) {
        String[] ids = Convert.toStrArray(orderIds);

        for (String id : ids) {
            Long orderId = Long.valueOf(id);

            // 释放库存
            SalesOrderDetail query = new SalesOrderDetail();
            query.setOrderId(orderId);
            List<SalesOrderDetail> details =
                    salesOrderDetailService.selectSalesOrderDetailList(query);

            for (SalesOrderDetail detail : details) {
                // 只处理未出库或部分出库的商品
                if (detail.getShipmentStatus() == 0 || detail.getShipmentStatus() == 1) {
                    Long remainingQuantity = detail.getModifiedQuantity() - detail.getShippedQuantity();
                    if (remainingQuantity > 0) {
                        // 释放锁定库存
                        Sku sku = skuService.selectSkuBySkuId(detail.getSkuId());
                        if (sku != null) {
                            sku.setLockedStock(sku.getLockedStock() - remainingQuantity);
                            sku.setAvailableStock(sku.getAvailableStock() + remainingQuantity);
                            skuService.updateSku(sku);
                        }
                    }
                }

                // 删除明细
                salesOrderDetailService.deleteSalesOrderDetailByDetailId(detail.getDetailId());
            }
        }

        return salesOrderMapper.deleteSalesOrderByOrderIds(ids);
    }

    /**
     * 更新销售订单及明细（包括优惠金额）
     */
    @Override
    @Transactional
    public int updateSalesOrderWithDiscounts(SalesOrder salesOrder, Long[] deletedDetailIds,
                                             Map<Long, Long> existingQuantities,
                                             Map<Long, BigDecimal> existingDiscounts,
                                             Map<Long, Long> existingSkuIds,
                                             Long[] newSkuIds,
                                             Long[] newQuantities, BigDecimal[] newUnitPrices,
                                             BigDecimal[] newDiscounts) {
        // 判断订单是否可修改
        SalesOrder originalOrder = salesOrderMapper.selectSalesOrderByOrderId(salesOrder.getOrderId());
        if (originalOrder != null && originalOrder.getCanModify() != null
                && originalOrder.getCanModify() == 0) {
            throw new RuntimeException("订单已锁定，不可修改");
        }

        // 先查询原有明细列表
        SalesOrderDetail queryDetail = new SalesOrderDetail();
        queryDetail.setOrderId(salesOrder.getOrderId());
        List<SalesOrderDetail> originalDetails = salesOrderDetailService.selectSalesOrderDetailList(queryDetail);

        // 处理订单明细和库存调整
        BigDecimal orderAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;

        // 1. 处理被删除的明细
        if (deletedDetailIds != null && deletedDetailIds.length > 0) {
            for (Long detailId : deletedDetailIds) {
                SalesOrderDetail detail = salesOrderDetailService.selectSalesOrderDetailByDetailId(detailId);
                if (detail != null && detail.getShipmentStatus() == 0) { // 只处理未出库的
                    // 释放锁定的库存
                    Sku sku = skuService.selectSkuBySkuId(detail.getSkuId());
                    if (sku != null) {
                        sku.setLockedStock(sku.getLockedStock() - detail.getModifiedQuantity());
                        sku.setAvailableStock(sku.getAvailableStock() + detail.getModifiedQuantity());
                        skuService.updateSku(sku);
                    }
                    // 删除明细
                    salesOrderDetailService.deleteSalesOrderDetailByDetailId(detailId);
                }
            }
        }

        // 2. 处理已有明细的数量、优惠金额和SKU更新
        if (existingQuantities != null && !existingQuantities.isEmpty()) {
            for (SalesOrderDetail detail : originalDetails) {
                // 跳过已删除的明细
                if (deletedDetailIds != null && Arrays.asList(deletedDetailIds).contains(detail.getDetailId())) {
                    continue;
                }

                // 获取新数量
                Long newQuantity = existingQuantities.get(detail.getDetailId());
                // 获取新优惠金额
                BigDecimal newDiscount = existingDiscounts != null ?
                        existingDiscounts.get(detail.getDetailId()) : detail.getDiscountAmount();
                // 获取新SKU ID
                Long newSkuId = existingSkuIds != null ?
                        existingSkuIds.get(detail.getDetailId()) : detail.getSkuId();

                if (newQuantity != null || newDiscount != null || newSkuId != null) {
                    // 处理SKU更换
                    if (newSkuId != null && !detail.getSkuId().equals(newSkuId)) {
                        // 释放原有SKU的库存
                        Sku oldSku = skuService.selectSkuBySkuId(detail.getSkuId());
                        if (oldSku != null) {
                            oldSku.setLockedStock(oldSku.getLockedStock() - detail.getModifiedQuantity());
                            oldSku.setAvailableStock(oldSku.getAvailableStock() + detail.getModifiedQuantity());
                            skuService.updateSku(oldSku);
                        }

                        // 更新为新SKU
                        Sku newSku = skuService.selectSkuBySkuId(newSkuId);
                        if (newSku != null) {
                            detail.setSkuId(newSkuId);
                            // 更新商品信息
                            Product product = productService.selectProductByProductId(newSku.getProductId());
                            if (product != null) {
                                detail.setProductName(product.getProductName());
                            }
                            detail.setSkuProperties(newSku.getColor() + " " + newSku.getSize());
                            detail.setUnitPrice(newSku.getSalePrice());
                            detail.setUnitWeight(newSku.getWeight());
                            detail.setUnitVolume(newSku.getVolume());

                            // 锁定新SKU的库存（使用更新后的数量）
                            Long finalQuantity = newQuantity != null ? newQuantity : detail.getModifiedQuantity();
                            newSku.setLockedStock(newSku.getLockedStock() + finalQuantity);
                            newSku.setAvailableStock(newSku.getAvailableStock() - finalQuantity);
                            skuService.updateSku(newSku);
                        }
                    } else {
                        // 没有更换SKU，只更新数量
                        if (newQuantity != null && !detail.getModifiedQuantity().equals(newQuantity)) {
                            Long originalQuantity = detail.getModifiedQuantity();
                            detail.setModifiedQuantity(newQuantity);

                            // 调整库存
                            Sku sku = skuService.selectSkuBySkuId(detail.getSkuId());
                            if (sku != null) {
                                Long quantityDiff = newQuantity - originalQuantity;
                                sku.setLockedStock(sku.getLockedStock() + quantityDiff);
                                sku.setAvailableStock(sku.getAvailableStock() - quantityDiff);
                                skuService.updateSku(sku);
                            }
                        }
                    }

                    // 更新数量（如果有新数量）
                    if (newQuantity != null) {
                        detail.setModifiedQuantity(newQuantity);
                    }

                    // 更新优惠金额
                    if (newDiscount != null) {
                        detail.setDiscountAmount(newDiscount);
                    }

                    // 计算金额
                    BigDecimal itemAmount = detail.getUnitPrice().multiply(new BigDecimal(detail.getModifiedQuantity()));
                    BigDecimal itemActualAmount = itemAmount.subtract(detail.getDiscountAmount());
                    detail.setTotalAmount(itemAmount);
                    detail.setActualAmount(itemActualAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : itemActualAmount);

                    // 更新明细
                    salesOrderDetailService.updateSalesOrderDetail(detail);

                    orderAmount = orderAmount.add(itemAmount);
                    discountAmount = discountAmount.add(detail.getDiscountAmount());
                }
            }
        }

        // 3. 处理新增的明细
        if (newSkuIds != null && newSkuIds.length > 0 && newQuantities != null && newQuantities.length > 0) {
            for (int i = 0; i < newSkuIds.length; i++) {
                if (i < newQuantities.length && newSkuIds[i] != null && newQuantities[i] != null) {
                    SalesOrderDetail detail = new SalesOrderDetail();
                    detail.setOrderId(salesOrder.getOrderId());
                    detail.setSkuId(newSkuIds[i]);
                    detail.setOriginalQuantity(newQuantities[i]);
                    detail.setModifiedQuantity(newQuantities[i]);

                    // 获取SKU信息
                    Sku sku = skuService.selectSkuBySkuId(newSkuIds[i]);
                    if (sku != null) {
                        // 设置商品信息
                        Product product = productService.selectProductByProductId(sku.getProductId());
                        if (product != null) {
                            detail.setProductName(product.getProductName());
                        }
                        detail.setSkuProperties(sku.getColor() + " " + sku.getSize());

                        // 设置单价
                        BigDecimal unitPrice = (i < newUnitPrices.length) ? newUnitPrices[i] : sku.getSalePrice();
                        detail.setUnitPrice(unitPrice != null ? unitPrice : sku.getSalePrice());

                        // 设置优惠
                        BigDecimal discount = (i < newDiscounts.length) ? newDiscounts[i] : BigDecimal.ZERO;
                        detail.setDiscountAmount(discount != null ? discount : BigDecimal.ZERO);

                        // 设置出库状态
                        detail.setShipmentStatus(0);
                        detail.setShippedQuantity(0L);

                        // 设置重量和体积
                        detail.setUnitWeight(sku.getWeight());
                        detail.setUnitVolume(sku.getVolume());

                        // 计算金额
                        BigDecimal itemAmount = detail.getUnitPrice().multiply(new BigDecimal(newQuantities[i]));
                        BigDecimal itemActualAmount = itemAmount.subtract(detail.getDiscountAmount());
                        detail.setTotalAmount(itemAmount);
                        detail.setActualAmount(itemActualAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : itemActualAmount);

                        // 添加明细
                        salesOrderDetailService.insertSalesOrderDetail(detail);

                        // 锁定库存
                        sku.setLockedStock(sku.getLockedStock() + newQuantities[i]);
                        sku.setAvailableStock(sku.getAvailableStock() - newQuantities[i]);
                        skuService.updateSku(sku);

                        // 累计金额
                        orderAmount = orderAmount.add(itemAmount);
                        discountAmount = discountAmount.add(detail.getDiscountAmount());
                    }
                }
            }
        }

        // 计算实付金额
        BigDecimal actualAmount = orderAmount.subtract(discountAmount);
        actualAmount = actualAmount.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : actualAmount;

        // 更新订单金额
        salesOrder.setOrderAmount(orderAmount);
        salesOrder.setDiscountAmount(discountAmount);
        salesOrder.setActualAmount(actualAmount);
        salesOrder.setUpdateTime(DateUtils.getNowDate());

        return salesOrderMapper.updateSalesOrder(salesOrder);
    }
}
