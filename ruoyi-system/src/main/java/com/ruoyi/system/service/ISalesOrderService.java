package com.ruoyi.system.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.ruoyi.system.domain.SalesOrder;

/**
 * 销售订单Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface ISalesOrderService
{
    /**
     * 查询销售订单
     *
     * @param orderId 销售订单主键
     * @return 销售订单
     */
    public SalesOrder selectSalesOrderByOrderId(Long orderId);

    /**
     * 查询销售订单列表
     *
     * @param salesOrder 销售订单
     * @return 销售订单集合
     */
    public List<SalesOrder> selectSalesOrderList(SalesOrder salesOrder);

    /**
     * 新增销售订单
     *
     * @param salesOrder 销售订单
     * @return 结果
     */
    public int insertSalesOrder(SalesOrder salesOrder);

    /**
     * 修改销售订单
     *
     * @param salesOrder 销售订单
     * @return 结果
     */
    public int updateSalesOrder(SalesOrder salesOrder);

    /**
     * 批量删除销售订单
     *
     * @param orderIds 需要删除的销售订单主键集合
     * @return 结果
     */
    public int deleteSalesOrderByOrderIds(String orderIds);

    /**
     * 删除销售订单信息
     *
     * @param orderId 销售订单主键
     * @return 结果
     */
    public int deleteSalesOrderByOrderId(Long orderId);

    /**
     * 创建销售订单
     * @param salesOrder
     * @param skuIds
     * @param quantities
     * @param unitPrices
     * @param discounts
     * @return
     */
    public int createSalesOrder(SalesOrder salesOrder, Long[] skuIds, Long[] quantities,
                                BigDecimal[] unitPrices, BigDecimal[] discounts);

    /**
     * 更新销售订单
     * @param salesOrder
     * @param deletedDetailIds
     * @param existingQuantities
     * @param newSkuIds
     * @param newQuantities
     * @param newUnitPrices
     * @param newDiscounts
     * @return
     */
    public int updateSalesOrderWithDetails(SalesOrder salesOrder, Long[] deletedDetailIds,
                                           Map<Long, Long> existingQuantities, Long[] newSkuIds,
                                           Long[] newQuantities, BigDecimal[] newUnitPrices,
                                           BigDecimal[] newDiscounts);


    /**
     * 更新销售订单
     * @param salesOrder
     * @param deletedDetailIds
     * @param existingQuantities
     * @param existingDiscounts
     * @param existingSkuIds
     * @param newSkuIds
     * @param newQuantities
     * @param newUnitPrices
     * @param newDiscounts
     * @return
     */
    public int updateSalesOrderWithDiscounts(SalesOrder salesOrder, Long[] deletedDetailIds,
                                             Map<Long, Long> existingQuantities,
                                             Map<Long, BigDecimal> existingDiscounts,
                                             Map<Long, Long> existingSkuIds,
                                             Long[] newSkuIds,
                                             Long[] newQuantities, BigDecimal[] newUnitPrices,
                                             BigDecimal[] newDiscounts);
}
